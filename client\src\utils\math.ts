// filepath: /hydrobuddy-pwa/hydrobuddy-pwa/client/src/utils/math.ts

export const add = (a: number, b: number): number => {
    return a + b;
};

export const subtract = (a: number, b: number): number => {
    return a - b;
};

export const multiply = (a: number, b: number): number => {
    return a * b;
};

export const divide = (a: number, b: number): number => {
    if (b === 0) {
        throw new Error("Division by zero is not allowed.");
    }
    return a / b;
};

export const average = (numbers: number[]): number => {
    const total = numbers.reduce((acc, num) => acc + num, 0);
    return total / numbers.length;
};