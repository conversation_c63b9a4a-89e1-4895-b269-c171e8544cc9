import React from 'react';
import { BrowserRouter as Router, Route, Routes } from 'react-router-dom';
import WaterQuality from './views/WaterQuality';
import SaltLibrary from './views/SaltLibrary';
import Formulation from './views/Formulation';
import Calculation from './views/Calculation';
import Results from './views/Results';
import Header from './components/Header';
import Footer from './components/Footer';

const Layout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <div className="app-container">
      <Header />
      <main className="main-content">
        {children}
      </main>
      <Footer />
    </div>
  );
};

const App: React.FC = () => {
  return (
    <Router>
      <Layout>
        <Routes>
          <Route path="/" element={<WaterQuality />} />
          <Route path="/salt-library" element={<SaltLibrary />} />
          <Route path="/formulation" element={<Formulation />} />
          <Route path="/calculation" element={<Calculation />} />
          <Route path="/results" element={<Results />} />
        </Routes>
      </Layout>
    </Router>
  );
};

export default App;