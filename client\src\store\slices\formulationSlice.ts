import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface Target {
  element: string;
  value: number;
  unit: string;
}

interface FormulationState {
  name: string;
  targets: Target[];
  loading: boolean;
  error: string | null;
  selectedFormulation: number | null;
}

const initialState: FormulationState = {
  name: 'New Formulation',
  targets: [],
  loading: false,
  error: null,
  selectedFormulation: null,
};

const formulationSlice = createSlice({
  name: 'formulation',
  initialState,
  reducers: {
    fetchFormulationsStart(state) {
      state.loading = true;
      state.error = null;
    },
    fetchFormulationsSuccess(state, action: PayloadAction<any>) {
      state.name = action.payload.name;
      state.targets = action.payload.targets;
      state.loading = false;
      state.error = null;
    },
    fetchFormulationsFailed(state, action: PayloadAction<string>) {
      state.loading = false;
      state.error = action.payload;
    },
    addTarget(state, action: PayloadAction<Target>) {
      state.targets.push(action.payload);
    },
    updateTarget(state, action: PayloadAction<{ index: number; target: Target }>) {
      const { index, target } = action.payload;
      if (index >= 0 && index < state.targets.length) {
        state.targets[index] = target;
      }
    },
    removeTarget(state, action: PayloadAction<number>) {
      state.targets = state.targets.filter((_, index) => index !== action.payload);
    },
    setFormulationName(state, action: PayloadAction<string>) {
      state.name = action.payload;
    },
    setSelectedFormulation(state, action: PayloadAction<number>) {
      state.selectedFormulation = action.payload;
    },
  },
});

export const {
  fetchFormulationsStart,
  fetchFormulationsSuccess,
  fetchFormulationsFailed,
  addTarget,
  updateTarget,
  removeTarget,
  setFormulationName,
  setSelectedFormulation,
} = formulationSlice.actions;

export default formulationSlice.reducer;
