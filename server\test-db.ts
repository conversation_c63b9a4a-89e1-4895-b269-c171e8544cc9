// test-db.ts
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  try {
    // Create a test element
    const element = await prisma.element.create({
      data: {
        symbol: 'N',
        name: '<PERSON>trogen',
        atomicWeight: 14.0067
      }
    });
    
    console.log('Successfully created element:', element);
    
    // Find all elements
    const elements = await prisma.element.findMany();
    console.log('All elements:', elements);
    
    console.log('Database connection successful!');
  } catch (error) {
    console.error('Error connecting to database:', error);
  } finally {
    await prisma.$disconnect();  // Properly disconnect from the database
  }
}

main();
