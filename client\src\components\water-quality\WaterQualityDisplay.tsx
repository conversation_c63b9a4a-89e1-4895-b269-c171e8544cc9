import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  Grid,
  Paper,
  Typography,
  Divider,
  Chip,
  Button,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Snackbar,
  Alert
} from '@mui/material';
import { exportToCsv, exportToJson, formatDateForFilename } from '../../utils/exportUtils';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import TableRowsIcon from '@mui/icons-material/TableRows';
import CodeIcon from '@mui/icons-material/Code';

interface WaterQualityDisplayProps {
  waterData: {
    name: string;
    ph: number;
    ec: number;
    tds: number;
    temperature: number;
    ca: number;
    mg: number;
    k: number;
    na: number;
    cl: number;
    so4: number;
    no3: number;
    hco3: number;
    co3: number;
  };
  ionUnit?: 'ppm' | 'meq' | 'mM';
}

const WaterQualityDisplay: React.FC<WaterQualityDisplayProps> = ({ 
  waterData, 
  ionUnit = 'ppm'
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };
  // Prepare export data for water quality
  const prepareExportData = () => {
    const waterProfileInfo = {
      name: waterData.name || 'Water Quality Profile',
      ph: waterData.ph,
      ec: `${waterData.ec} mS/cm`,
      tds: `${waterData.tds} ppm`,
      temperature: `${waterData.temperature} °C`,
      hardness: {
        dGH: `${dGH} °dH`,
        category: hardnessCategory.label
      },
      alkalinity: {
        dKH: `${dKH} °dH`,
        category: alkalinityCategory.label
      },
      ions: {
        unit: ionUnit,
        cations: {
          ca: waterData.ca,
          mg: waterData.mg,
          k: waterData.k,
          na: waterData.na
        },
        anions: {
          cl: waterData.cl,
          so4: waterData.so4,
          no3: waterData.no3,
          hco3: waterData.hco3,
          co3: waterData.co3
        }
      }
    };
    
    return waterProfileInfo;
  };

  const handleExportCsv = () => {
    try {
      const data = prepareExportData();
      
      // Create table headers and rows
      const headers = ['Property', 'Value'];
      
      // Add water profile information rows
      const waterInfoRows = [
        ['Profile Name', data.name],
        ['pH', data.ph.toString()],
        ['EC', data.ec],
        ['TDS', data.tds],
        ['Temperature', data.temperature],
        ['German Hardness (dGH)', data.hardness.dGH],
        ['Hardness Category', data.hardness.category],
        ['Carbonate Hardness (dKH)', data.alkalinity.dKH],
        ['Alkalinity Category', data.alkalinity.category],
        [''] // Empty row as separator
      ];
      
      // Add ion data
      const ionRows = [
        ['Ion Concentrations', `Unit: ${data.ions.unit}`],
        ['Calcium (Ca²⁺)', data.ions.cations.ca.toString()],
        ['Magnesium (Mg²⁺)', data.ions.cations.mg.toString()],
        ['Potassium (K⁺)', data.ions.cations.k.toString()],
        ['Sodium (Na⁺)', data.ions.cations.na.toString()],
        ['Chloride (Cl⁻)', data.ions.anions.cl.toString()],
        ['Sulfate (SO₄²⁻)', data.ions.anions.so4.toString()],
        ['Nitrate (NO₃⁻)', data.ions.anions.no3.toString()],
        ['Bicarbonate (HCO₃⁻)', data.ions.anions.hco3.toString()],
        ['Carbonate (CO₃²⁻)', data.ions.anions.co3.toString()]
      ];
      
      // Combine rows
      const allRows = [...waterInfoRows, ...ionRows];
      
      // Export using utility function
      exportToCsv(headers, allRows, `${data.name.replace(/\s+/g, '_')}_water_quality`);
      setSnackbarMessage('Water quality data exported as CSV successfully');
      handleMenuClose();
    } catch (error) {
      console.error('Error exporting to CSV:', error);
      setSnackbarMessage('Failed to export water quality data as CSV');
      setSnackbarOpen(true);
    }
  };

  const handleExportJson = () => {
    try {
      const data = prepareExportData();
      
      const exportData = {
        waterProfile: data,
        exportDate: new Date().toISOString()
      };
      
      exportToJson(exportData, `${data.name.replace(/\s+/g, '_')}_water_quality`);
      setSnackbarMessage('Water quality data exported as JSON successfully');
      setSnackbarOpen(true);
      handleMenuClose();
    } catch (error) {
      console.error('Error exporting to JSON:', error);
      setSnackbarMessage('Failed to export water quality data as JSON');
      setSnackbarOpen(true);
    }
  };

  const calculateDGH = (ca: number, mg: number): number => {
    if (ionUnit === 'ppm') {
      return Math.round(((ca / 7.14) + (mg / 4.33)) * 10) / 10;
    } else if (ionUnit === 'meq') {
      return Math.round((ca * 2.5 + mg * 4.1) * 10) / 10;
    } else {
      return Math.round((ca * 5 + mg * 8.2) * 10) / 10;
    }
  };

  const calculateDKH = (hco3: number, co3: number): number => {
    if (ionUnit === 'ppm') {
      return Math.round(((hco3 / 21.8) + (co3 / 10.9)) * 10) / 10;
    } else if (ionUnit === 'meq') {
      return Math.round((hco3 + co3 * 2) * 10) / 10;
    } else {
      return Math.round((hco3 + co3 * 2) * 10) / 10;
    }
  };

  const getWaterHardnessCategory = (dGH: number): {
    label: string;
    color: "success" | "info" | "warning" | "error" | "default" | "primary" | "secondary";
  } => {
    if (dGH < 4) {
      return { label: "Soft", color: "success" };
    } else if (dGH < 8) {
      return { label: "Moderately Soft", color: "info" };
    } else if (dGH < 12) {
      return { label: "Moderately Hard", color: "warning" };
    } else if (dGH < 18) {
      return { label: "Hard", color: "error" };
    } else {
      return { label: "Very Hard", color: "error" };
    }
  };

  const getAlkalinityCategory = (dKH: number): {
    label: string;
    color: "success" | "info" | "warning" | "error" | "default" | "primary" | "secondary";
  } => {
    if (dKH < 1) {
      return { label: "Very Low", color: "error" };
    } else if (dKH < 3) {
      return { label: "Low", color: "warning" };
    } else if (dKH < 6) {
      return { label: "Moderate", color: "info" };
    } else if (dKH < 10) {
      return { label: "High", color: "success" };
    } else {
      return { label: "Very High", color: "error" };
    }
  };

  const dGH = calculateDGH(waterData.ca, waterData.mg);
  const dKH = calculateDKH(waterData.hco3, waterData.co3);
  const hardnessCategory = getWaterHardnessCategory(dGH);
  const alkalinityCategory = getAlkalinityCategory(dKH);

  return (
    <Card>
      <CardHeader
        title={waterData.name || "Water Quality Profile"}
        subheader="Water source parameters and composition"
        action={
          <div>
            <Button 
              aria-controls="export-menu" 
              aria-haspopup="true" 
              onClick={handleMenuClick}
              color="primary"
              size="small"
            >
              <FileDownloadIcon fontSize="small" />
              Export
            </Button>
            <Menu
              id="export-menu"
              anchorEl={anchorEl}
              keepMounted
              open={Boolean(anchorEl)}
              onClose={handleMenuClose}
            >
              <MenuItem onClick={handleExportCsv}>
                <ListItemIcon>
                  <TableRowsIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText primary="Export as CSV" />
              </MenuItem>
              <MenuItem onClick={handleExportJson}>
                <ListItemIcon>
                  <CodeIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText primary="Export as JSON" />
              </MenuItem>
            </Menu>
          </div>
        }
      />
      <CardContent>
        <Grid container spacing={3}>
          {/* Basic Parameters */}
          <Grid item xs={12}>
            <Paper elevation={1} sx={{ p: 2 }}>
              <Typography variant="h6" gutterBottom>Basic Parameters</Typography>
              <Grid container spacing={2}>
                <Grid item xs={6} md={3}>
                  <Typography variant="body2" color="text.secondary">pH</Typography>
                  <Typography variant="h6">{waterData.ph}</Typography>
                </Grid>
                <Grid item xs={6} md={3}>
                  <Typography variant="body2" color="text.secondary">EC</Typography>
                  <Typography variant="h6">{waterData.ec} mS/cm</Typography>
                </Grid>
                <Grid item xs={6} md={3}>
                  <Typography variant="body2" color="text.secondary">TDS</Typography>
                  <Typography variant="h6">{waterData.tds} ppm</Typography>
                </Grid>
                <Grid item xs={6} md={3}>
                  <Typography variant="body2" color="text.secondary">Temperature</Typography>
                  <Typography variant="h6">{waterData.temperature} °C</Typography>
                </Grid>
              </Grid>
            </Paper>
          </Grid>

          {/* Water Hardness Section */}
          <Grid item xs={12} md={6}>
            <Paper elevation={1} sx={{ p: 2 }}>
              <Grid container justifyContent="space-between" alignItems="center">
                <Typography variant="h6">Water Hardness</Typography>
                <Chip 
                  label={hardnessCategory.label} 
                  color={hardnessCategory.color} 
                  size="small"
                />
              </Grid>
              <Divider sx={{ my: 1 }} />
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">German Hardness (dGH)</Typography>
                  <Typography variant="h6">{dGH} °dH</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">Calcium (Ca²⁺)</Typography>
                  <Typography variant="h6">
                    {waterData.ca} {ionUnit}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">Magnesium (Mg²⁺)</Typography>
                  <Typography variant="h6">
                    {waterData.mg} {ionUnit}
                  </Typography>
                </Grid>
              </Grid>
            </Paper>
          </Grid>

          {/* Alkalinity Section */}
          <Grid item xs={12} md={6}>
            <Paper elevation={1} sx={{ p: 2 }}>
              <Grid container justifyContent="space-between" alignItems="center">
                <Typography variant="h6">Alkalinity</Typography>
                <Chip 
                  label={alkalinityCategory.label} 
                  color={alkalinityCategory.color} 
                  size="small"
                />
              </Grid>
              <Divider sx={{ my: 1 }} />
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">Carbonate Hardness (dKH)</Typography>
                  <Typography variant="h6">{dKH} °dH</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">Bicarbonate (HCO₃⁻)</Typography>
                  <Typography variant="h6">
                    {waterData.hco3} {ionUnit}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">Carbonate (CO₃²⁻)</Typography>
                  <Typography variant="h6">
                    {waterData.co3} {ionUnit}
                  </Typography>
                </Grid>
              </Grid>
            </Paper>
          </Grid>

          {/* Ion Balance Section */}
          <Grid item xs={12}>
            <Paper elevation={1} sx={{ p: 2 }}>
              <Typography variant="h6" gutterBottom>Ion Composition</Typography>
              <Grid container spacing={2}>
                {/* Cations */}
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>Cations</Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Potassium (K⁺)</Typography>
                      <Typography>
                        {waterData.k} {ionUnit}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Sodium (Na⁺)</Typography>
                      <Typography>
                        {waterData.na} {ionUnit}
                      </Typography>
                    </Grid>
                  </Grid>
                </Grid>

                {/* Anions */}
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>Anions</Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Chloride (Cl⁻)</Typography>
                      <Typography>
                        {waterData.cl} {ionUnit}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Sulfate (SO₄²⁻)</Typography>
                      <Typography>
                        {waterData.so4} {ionUnit}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Nitrate (NO₃⁻)</Typography>
                      <Typography>
                        {waterData.no3} {ionUnit}
                      </Typography>
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </Paper>
          </Grid>
        </Grid>
      </CardContent>
      <Snackbar 
        open={snackbarOpen} 
        autoHideDuration={6000} 
        onClose={() => setSnackbarOpen(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={() => setSnackbarOpen(false)} severity="success" sx={{ width: '100%' }}>
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Card>
  );
};

export default WaterQualityDisplay;
