import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  TextField,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Grid,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Divider,
  Alert,
  Box,
  Tooltip,
  CircularProgress
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import InfoIcon from '@mui/icons-material/Info';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store';
import axios from 'axios';
import { 
  calculateMolecularWeight, 
  isValidComposition,
  calculateStockSolutionConcentration,
  ELEMENTS
} from '../../utils/elementCalculations';

interface ElementComposition {
  symbol: string;
  percentage: number;
  atomicWeight?: number;
}

interface SaltFormData {
  name: string;
  formula: string;
  density: number | null;
  solubility: number | null;
  isCustom: boolean;
  elements: ElementComposition[];
}

const initialSaltFormData: SaltFormData = {
  name: '',
  formula: '',
  density: null,
  solubility: null,
  isCustom: true,
  elements: []
};

const SaltCompositionCalculator: React.FC = () => {
  const [saltFormData, setSaltFormData] = useState<SaltFormData>(initialSaltFormData);
  const [availableElements, setAvailableElements] = useState<{ symbol: string, name: string, atomicWeight: number }[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [saveSuccess, setSaveSuccess] = useState<boolean | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [elementSelection, setElementSelection] = useState<string>('');
  const [elementPercentage, setElementPercentage] = useState<string>('');
  const [totalPercentage, setTotalPercentage] = useState<number>(0);

  // Fetch elements from API
  useEffect(() => {
    const fetchElements = async () => {
      try {
        setLoading(true);
        const response = await axios.get('http://localhost:3001/api/elements');
        setAvailableElements(response.data);
      } catch (error) {
        console.error('Error fetching elements:', error);
        setErrorMessage('Failed to fetch elements. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchElements();
  }, []);

  // Calculate total percentage whenever elements change
  useEffect(() => {
    const total = saltFormData.elements.reduce((sum, elem) => sum + elem.percentage, 0);
    setTotalPercentage(Number(total.toFixed(2)));
  }, [saltFormData.elements]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setSaltFormData({ ...saltFormData, [name]: value });
  };

  const handleNumberInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    if (value === '') {
      setSaltFormData({ ...saltFormData, [name]: null });
    } else {
      const numberValue = parseFloat(value);
      if (!isNaN(numberValue)) {
        setSaltFormData({ ...saltFormData, [name]: numberValue });
      }
    }
  };

  const handleElementSelectionChange = (event: any) => {
    setElementSelection(event.target.value);
  };

  const handlePercentageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setElementPercentage(e.target.value);
  };

  const addElement = () => {
    if (!elementSelection) {
      setErrorMessage('Please select an element');
      return;
    }

    const percentage = parseFloat(elementPercentage);
    if (isNaN(percentage) || percentage <= 0 || percentage > 100) {
      setErrorMessage('Please enter a valid percentage between 0 and 100');
      return;
    }

    // Check if element already exists
    const existingIndex = saltFormData.elements.findIndex(e => e.symbol === elementSelection);
    if (existingIndex !== -1) {
      setErrorMessage('This element is already in the composition. Please edit it instead.');
      return;
    }

    const selectedElement = availableElements.find(elem => elem.symbol === elementSelection);
    if (!selectedElement) {
      setErrorMessage('Invalid element selected');
      return;
    }

    const newElement: ElementComposition = {
      symbol: elementSelection,
      percentage: percentage,
      atomicWeight: selectedElement.atomicWeight
    };

    setSaltFormData({
      ...saltFormData,
      elements: [...saltFormData.elements, newElement]
    });

    // Clear selection fields
    setElementSelection('');
    setElementPercentage('');
    setErrorMessage(null);
  };

  const removeElement = (symbol: string) => {
    setSaltFormData({
      ...saltFormData,
      elements: saltFormData.elements.filter(elem => elem.symbol !== symbol)
    });
  };

  const handleSave = async () => {
    // Validate form
    if (!saltFormData.name) {
      setErrorMessage('Salt name is required');
      return;
    }

    if (saltFormData.elements.length === 0) {
      setErrorMessage('At least one element is required in the composition');
      return;
    }
    
    // Validate total composition percentage is close to 100%
    const totalPercentage = parseFloat(getTotalPercentage());
    if (Math.abs(totalPercentage - 100) > 5) {
      setErrorMessage(`Total element percentage (${totalPercentage}%) is too far from 100%. Please adjust element percentages.`);
      return;
    }

    try {
      setLoading(true);
      
      // Format data for API
      const apiData = {
        name: saltFormData.name,
        formula: saltFormData.formula || null,
        isCustom: true,
        density: saltFormData.density,
        solubility: saltFormData.solubility,
        elements: saltFormData.elements.map(elem => ({
          symbol: elem.symbol,
          percentage: elem.percentage
        }))
      };

      // Save to API
      const response = await axios.post('http://localhost:3001/api/salts', apiData);
      
      setSaveSuccess(true);
      setErrorMessage(null);
      
      // Reset form after successful save
      setTimeout(() => {
        setSaltFormData(initialSaltFormData);
        setSaveSuccess(null);
      }, 3000);
    } catch (error) {
      console.error('Error saving salt:', error);
      setSaveSuccess(false);
      setErrorMessage('Failed to save salt. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const validateComposition = () => {
    return isValidComposition(saltFormData.elements);
  };
  
  const getTotalPercentage = () => {
    return saltFormData.elements.reduce((sum, elem) => sum + elem.percentage, 0).toFixed(2);
  };
  
  const getStockSolutionConcentration = () => {
    if (!saltFormData.solubility) return null;
    
    // Calculate for 10L stock solution by default
    return calculateStockSolutionConcentration(saltFormData.solubility, 10);
  };
  return (
    <Card sx={{ mb: 4 }}>
      <CardHeader 
        title="Salt Composition Calculator" 
        subheader="Create and analyze chemical salt compositions for hydroponics"
      />
      <CardContent>
        <Grid container spacing={3}>
          {/* Basic Salt Information */}
          <Grid size={12}>
            <Paper elevation={1} sx={{ p: 2, mb: 2 }}>
              <Typography variant="h6" gutterBottom>Basic Information</Typography>
              <Grid container spacing={2}>
                <Grid size={{ xs: 12, md: 6 }}>
                  <TextField
                    fullWidth
                    label="Salt Name"
                    name="name"
                    value={saltFormData.name}
                    onChange={handleInputChange}
                    required
                    margin="normal"
                  />
                </Grid>
                <Grid size={{ xs: 12, md: 6 }}>
                  <TextField
                    fullWidth
                    label="Chemical Formula"
                    name="formula"
                    value={saltFormData.formula}
                    onChange={handleInputChange}
                    margin="normal"
                    placeholder="e.g., KNO3, Ca(NO3)2"
                  />
                </Grid>
                <Grid size={{ xs: 12, md: 6 }}>
                  <TextField
                    fullWidth
                    label="Density (g/cm³)"
                    name="density"
                    type="number"
                    value={saltFormData.density === null ? '' : saltFormData.density}
                    onChange={handleNumberInputChange}
                    margin="normal"
                    inputProps={{ step: "0.01", min: "0" }}
                  />
                </Grid>
                <Grid size={{ xs: 12, md: 6 }}>
                  <TextField
                    fullWidth
                    label="Solubility (g/L at 20°C)"
                    name="solubility"
                    type="number"
                    value={saltFormData.solubility === null ? '' : saltFormData.solubility}
                    onChange={handleNumberInputChange}
                    margin="normal"
                    inputProps={{ step: "0.1", min: "0" }}
                  />
                </Grid>
              </Grid>
            </Paper>
          </Grid>

          {/* Element Composition Section */}
          <Grid size={12}>
            <Paper elevation={1} sx={{ p: 2 }}>
              <Typography variant="h6" gutterBottom>Element Composition</Typography>
              
              <Grid container spacing={2} alignItems="center" sx={{ mb: 2 }}>
                <Grid size={{ xs: 12, md: 5 }}>
                  <FormControl fullWidth disabled={loading}>
                    <InputLabel id="element-select-label">Element</InputLabel>
                    <Select
                      labelId="element-select-label"
                      value={elementSelection}
                      onChange={handleElementSelectionChange}
                      label="Element"
                    >
                      {availableElements.map((element) => (
                        <MenuItem key={element.symbol} value={element.symbol}>
                          {element.symbol} - {element.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid size={{ xs: 8, md: 3 }}>
                  <TextField
                    fullWidth
                    label="Percentage"
                    type="number"
                    value={elementPercentage}
                    onChange={handlePercentageChange}
                    inputProps={{ step: "0.01", min: "0", max: "100" }}
                    disabled={loading}
                  />
                </Grid>
                <Grid size={{ xs: 4, md: 2 }}>
                  <Typography variant="body2" color="text.secondary" sx={{ whiteSpace: 'nowrap' }}>
                    % by weight
                  </Typography>
                </Grid>
                <Grid size={{ xs: 12, md: 2 }}>
                  <Button 
                    variant="contained" 
                    startIcon={<AddIcon />} 
                    onClick={addElement}
                    fullWidth
                    disabled={loading || !elementSelection || !elementPercentage}
                  >
                    Add
                  </Button>
                </Grid>
              </Grid>
              
              {errorMessage && (
                <Alert severity="error" sx={{ mb: 2 }}>{errorMessage}</Alert>
              )}
              
              {saveSuccess === true && (
                <Alert severity="success" sx={{ mb: 2 }}>Salt saved successfully!</Alert>
              )}
              
              {saveSuccess === false && (
                <Alert severity="error" sx={{ mb: 2 }}>Failed to save salt. Please try again.</Alert>
              )}
              
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Element</TableCell>
                      <TableCell>Symbol</TableCell>
                      <TableCell align="right">Percentage (%)</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {saltFormData.elements.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={4} align="center">
                          No elements added yet
                        </TableCell>
                      </TableRow>
                    ) : (
                      saltFormData.elements.map((elem) => {
                        const elementDetails = availableElements.find(e => e.symbol === elem.symbol);
                        return (
                          <TableRow key={elem.symbol}>
                            <TableCell>{elementDetails?.name || 'Unknown'}</TableCell>
                            <TableCell>{elem.symbol}</TableCell>
                            <TableCell align="right">{elem.percentage.toFixed(2)}</TableCell>
                            <TableCell align="right">
                              <IconButton 
                                size="small" 
                                color="error"
                                onClick={() => removeElement(elem.symbol)}
                              >
                                <DeleteIcon />
                              </IconButton>
                            </TableCell>
                          </TableRow>
                        );
                      })
                    )}
                    {saltFormData.elements.length > 0 && (
                      <TableRow sx={{ '& td': { fontWeight: 'bold', borderTop: '1px solid rgba(224, 224, 224, 1)' } }}>
                        <TableCell colSpan={2}>Total</TableCell>
                        <TableCell align="right">{totalPercentage}%</TableCell>
                        <TableCell></TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
              
              {totalPercentage > 0 && totalPercentage !== 100 && (
                <Alert 
                  severity={Math.abs(totalPercentage - 100) > 5 ? "warning" : "info"} 
                  sx={{ mt: 2 }}
                >
                  Total composition is {totalPercentage}%. Ideally, it should sum to 100%. 
                  {Math.abs(totalPercentage - 100) > 5 && " Please adjust element percentages before saving."}
                </Alert>
              )}
              
              {saltFormData.elements.length > 0 && (
                <Box sx={{ mt: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
                  <Typography variant="subtitle1" gutterBottom>
                    Calculated Properties
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid size={{ xs: 12, md: 6 }}>
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          Estimated Molecular Weight
                        </Typography>
                        <Typography variant="h6">
                          {calculateMolecularWeight(saltFormData.elements) || 'N/A'} g/mol
                        </Typography>
                      </Box>
                    </Grid>
                    {saltFormData.solubility && (
                      <Grid size={{ xs: 12, md: 6 }}>
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            Max concentration in 10L stock solution at 20°C
                          </Typography>
                          <Typography variant="h6">
                            {getStockSolutionConcentration()?.toFixed(0) || 'N/A'} g
                          </Typography>
                        </Box>
                      </Grid>
                    )}
                  </Grid>
                </Box>
              )}
            </Paper>
          </Grid>

          {/* Save Button */}
          <Grid size={12}>
            <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
              <Button 
                variant="contained" 
                color="primary"
                disabled={loading || saltFormData.elements.length === 0 || !saltFormData.name}
                onClick={handleSave}
                sx={{ minWidth: 120 }}
              >
                {loading ? <CircularProgress size={24} /> : 'Save Salt'}
              </Button>
            </Box>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
};

export default SaltCompositionCalculator;
