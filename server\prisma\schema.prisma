// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

model Element {
  id             String           @id @default(cuid())
  symbol         String           @unique
  name           String
  atomicWeight   Float
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  saltElements   SaltElement[]
  targetElements TargetElement[]
}

model Salt {
  id           String            @id @default(cuid())
  name         String
  formula      String?
  isCustom     Boolean           @default(false)
  density      Float?
  solubility   Float?
  createdAt    DateTime          @default(now())
  updatedAt    DateTime          @updatedAt
  elements     SaltElement[]
  formulations FormulationSalt[]
}

model SaltElement {
  id        String   @id @default(cuid())
  salt      Salt     @relation(fields: [saltId], references: [id])
  saltId    String
  element   Element  @relation(fields: [elementId], references: [id])
  elementId String
  percentage Float
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([saltId, elementId])
}

model WaterProfile {
  id          String         @id @default(cuid())
  name        String
  description String?
  ec          Float
  ph          Float
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt
  elements    WaterElement[]
  formulations Formulation[]
}

model WaterElement {
  id             String       @id @default(cuid())
  waterProfile   WaterProfile @relation(fields: [waterProfileId], references: [id])
  waterProfileId String
  elementSymbol  String
  concentration  Float
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt

  @@unique([waterProfileId, elementSymbol])
}

model Formulation {
  id             String            @id @default(cuid())
  name           String
  description    String?
  waterProfile   WaterProfile      @relation(fields: [waterProfileId], references: [id])
  waterProfileId String
  targetEc       Float?
  targetPh       Float?
  volume         Float
  volumeUnit     String            @default("L")
  createdAt      DateTime          @default(now())
  updatedAt      DateTime          @updatedAt
  salts          FormulationSalt[]
  targetElements TargetElement[]
}

model FormulationSalt {
  id            String     @id @default(cuid())
  formulation   Formulation @relation(fields: [formulationId], references: [id])
  formulationId String
  salt          Salt       @relation(fields: [saltId], references: [id])
  saltId        String
  amount        Float
  unit          String     @default("g")
  createdAt     DateTime   @default(now())
  updatedAt     DateTime   @updatedAt

  @@unique([formulationId, saltId])
}

model TargetElement {
  id            String     @id @default(cuid())
  formulation   Formulation @relation(fields: [formulationId], references: [id])
  formulationId String
  element       Element    @relation(fields: [elementId], references: [id])
  elementId     String
  concentration Float
  unit          String     @default("ppm")
  createdAt     DateTime   @default(now())
  updatedAt     DateTime   @updatedAt

  @@unique([formulationId, elementId])
}
