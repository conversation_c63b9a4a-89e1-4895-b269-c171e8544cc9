<!-- @format -->

# HydroBuddy End-to-End Calculation & Preparation Sequence

This document consolidates the complete Pascal workflow of HydroBuddy—from user inputs through each calculation and solution preparation method to final reporting—listing each procedure, its unit, models, and equations.

---

## References & Models Used

- **Pascal Units:**

  - `HB_Main.pas` (main form, UI logic)
  - `hb_waterquality.pas` (water‐quality load & adjustment)
  - `hb_load_salts.pas` (salt definitions loader)
  - `hb_newcustomsalt.pas` (user‐added salts)
  - `densesolver.pas` (Gaussian elimination & least‐squares)
  - `hb_addweight.pas` (batch mass computation)
  - `hb_insprecision.pas` (rounding & residual correction)
  - `hb_stockanalysis.pas` (solution preparation types)
  - `hb_commercialnutrient.pas` (commercial product mapping)
  - `hb_persubstance.pas` (elemental breakdown)
  - `hb_ph.pas` (pH estimation via Henderson–Hasselbalch)
  - `hb_ratios.pas` (elemental ratios)
  - `hb_comparison.pas` (standards comparison)
  - `hb_tissue_analysis.pas` (tissue uptake model)

- **Mathematical Models & Equations:**
  1. **Linear system**: \(A\mathbf{x}=\mathbf{T}\), solved by Gaussian elimination and back‐substitution; least‐squares fallback \((A^T A)^{-1}A^T\mathbf{T}\).
  2. **Mass balance**: \(M_i = x_i \times V\) for batch masses.
  3. **Rounding & correction**: round to \(10^{-d}\) g and adjust based on residuals.
  4. **Solution preparation**: direct batch, single stock, two-part, continuous feed.
  5. **Cost model**: linear cost = mass \(\times\) cost_per_gram.
  6. **Elemental breakdown**: \(c*{j,i}=e*{j,i}\,x_i\).
  7. **pH**: Henderson–Hasselbalch \(pH = pK*a + \log*{10}(\tfrac{[A^-]}{[HA]})\).
  8. **Tissue uptake**: \(C*{tissue}=R*{appl}\times u_j\).

---

## THydroBuddy.PerformCalculation (HB_Main.pas)

```pascal
procedure THydroBuddy.PerformCalculation;
begin
  LoadWaterQuality;          // hb_waterquality.pas
  LoadAvailableSalts;        // hb_load_salts.pas
  LoadCustomSalts;           // hb_newcustomsalt.pas
  LoadTargetFormulation;     // HB_Main.pas
  CheckAndConvertUnits;      // HB_Main.pas

  AdjustForWaterQuality;     // hb_waterquality.pas
  BuildStoichiometricMatrix; // densesolver.pas
  SolveSaltWeights;          // densesolver.pas

  ComputeSolutionWeights;    // hb_addweight.pas
  RefinePrecision;           // hb_insprecision.pas
  PrepareSolutionType;       // hb_stockanalysis.pas

  ConvertToCommercialMix;    // hb_commercialnutrient.pas
  BreakdownPerSubstance;      // hb_persubstance.pas
  ComputePHEffect;           // hb_ph.pas
  ComputeElementRatios;      // hb_ratios.pas
  CompareWithStandards;      // hb_comparison.pas

  if Form1.PerformTissueAnalysis then
    PerformTissueAnalysis;    // hb_tissue_analysis.pas

  UpdateGridsAndCharts;      // UI refresh, tachartlazaruspkg
  ShowSummaryReport;         // export/dialog
end;
```

---

### 1. LoadWaterQuality (hb_waterquality.pas)

```pascal
procedure THydroBuddy.LoadWaterQuality;
var
  db: TDbf;
  j: Integer;
begin
  if Form1.rbWaterQualityFromDB.Checked then
  begin
    db := TDbf.Create(nil);
    try
      db.FilePathFull := ExtractFilePath(Application.ExeName) + Form1.water_quality_db;
      db.Open;
      while not db.EOF do
      begin
        if db.FieldByName('Default').AsInteger = 1 then
        begin
          for j := 0 to High(Elements) do
            WaterQuality[j] := db.FieldByName(Elements[j]).AsFloat;
          Break;
        end;
        db.Next;
      end;
    finally db.Free; end;
  end
  else // manual
    for j := 0 to High(Elements) do
      WaterQuality[j] := StrToFloat(Form1.WQEdits[j].Text);

  for j := 0 to High(WaterQuality) do
    if WaterQuality[j] < 0 then
      raise Exception.CreateFmt('Invalid WQ for %s', [Elements[j]]);
end;
```

### 2. LoadAvailableSalts (hb_load_salts.pas)

```pascal
procedure THydroBuddy.LoadAvailableSalts;
begin
  hb_load_salts.LoadSalts(SaltsList);
end;
```

### 3. LoadCustomSalts (hb_newcustomsalt.pas)

```pascal
procedure THydroBuddy.LoadCustomSalts;
begin
  hb_newcustomsalt.MergeCustomSalts(SaltsList);
end;
```

### 4. LoadTargetFormulation (HB_Main.pas)

```pascal
procedure THydroBuddy.LoadTargetFormulation;
var j: Integer; db: TDbf;
begin
  if Form1.rbLoadFromDB.Checked then
  begin
    db := TDbf.Create(nil);
    try
      db.FilePathFull := ExtractFilePath(Application.ExeName) + Form1.target_formulations_db;
      db.Open;
      while not db.EOF do
      begin
        if db.FieldByName('Default').AsInteger = 1 then
        begin
          for j := 0 to High(Elements) do
            Targets[j] := db.FieldByName(Elements[j]).AsFloat;
          Break; end;
        db.Next;
      end;
    finally db.Free; end;
  end
  else
    for j := 0 to High(Elements) do
      Targets[j] := StrToFloat(Form1.ElementEdits[j].Text);
end;
```

### 4.1 CheckAndConvertUnits (HB_Main.pas)

```pascal
function THydroBuddy.ConvertMass(val: Double; unitStr: string): Double;
begin
  if unitStr='mg' then Result:=val*0.001
  else if unitStr='g' then Result:=val
  else if unitStr='kg' then Result:=val*1000
  else raise Exception.Create('Unknown mass unit');
end;

function THydroBuddy.ConvertVolume(val: Double; unitStr: string): Double;
begin
  if unitStr='mL' then Result:=val*0.001
  else if unitStr='L' then Result:=val
  else if unitStr='m3' then Result:=val*1000
  else raise Exception.Create('Unknown vol unit');
end;

procedure THydroBuddy.CheckAndConvertUnits;
var j: Integer; raw, mU,vU: string;
begin
  for j:=0 to High(Elements) do
  begin
    raw:=Form1.ElementEdits[j].Text;
    mU:=Form1.TargetMassUnitCombo[j].Text;
    vU:=Form1.TargetVolUnitCombo[j].Text;
    Targets[j]:=ConvertMass(StrToFloat(raw),mU)/ConvertVolume(1,vU);
  end;
  for j:=0 to High(Elements) do
  begin
    raw:=Form1.WQEdits[j].Text;
    mU:=Form1.WQMassUnitCombo[j].Text;
    vU:=Form1.WQVolUnitCombo[j].Text;
    WaterQuality[j]:=ConvertMass(StrToFloat(raw),mU)/ConvertVolume(1,vU);
  end;
end;
```

### 5. AdjustForWaterQuality (hb_waterquality.pas)

```pascal
procedure THydroBuddy.AdjustForWaterQuality;
var j: Integer;
begin
  for j:=0 to High(Targets) do
    Targets[j]:=Max(0,Targets[j]-WaterQuality[j]);
end;
```

### 6. BuildStoichiometricMatrix & SolveSaltWeights (densesolver.pas)

```pascal
// A[j,i]=e[j,i], B[j]=Targets[j]
procedure THydroBuddy.BuildStoichiometricMatrix;
var i,j:Integer;
begin
  SetLength(A,Length(Elements),Length(SaltsList));
  SetLength(B,Length(Elements));
  for j:=0 to High(Elements) do
  begin
    B[j]:=Targets[j];
    for i:=0 to High(SaltsList) do
      A[j,i]:=SaltsList[i].MassFractions[Elements[j]];
  end;
end;

procedure THydroBuddy.SolveSaltWeights;
var m,n,k,row,col,pivotRow:Integer; factor,pivot,maxVal,sum:Double;
begin
  m:=Length(B); n:=Length(SaltsList);
  // build AMat[m][n+1]
  // forward elimination w/ pivoting
  for k:=0 to Min(m,n)-2 do
  begin
    // pivot
    pivotRow:=k; maxVal:=Abs(AMat[k][k]);
    for row:=k+1 to m-1 do
      if Abs(AMat[row][k])>maxVal then begin maxVal:=Abs(AMat[row][k]); pivotRow:=row; end;
    if pivotRow<>k then SwapRows(k,pivotRow);
    pivot:=AMat[k][k];
    if Abs(pivot)<1e-12 then raise Exception.Create('Singular');
    for row:=k+1 to m-1 do begin factor:=AMat[row][k]/pivot; for col:=k to n do AMat[row][col]:=AMat[row][col]-factor*AMat[k][col]; end;
  end;
  // back-sub
  SetLength(X,n);
  for row:=Min(m,n)-1 downto 0 do
  begin sum:=0; for col:=row+1 to n-1 do sum+=AMat[row][col]*X[col]; pivot:=AMat[row][row]; if Abs(pivot)<1e-12 then raise Exception.Create('Zero pivot'); X[row]:=(AMat[row][n]-sum)/pivot; end;
  SaltConcentrations:=Copy(X,0,n);
end;
```

### 7. ComputeSolutionWeights (hb_addweight.pas)

```pascal
procedure THydroBuddy.ComputeSolutionWeights;
var i:Integer;
begin
  SetLength(BatchMasses,Length(SaltConcentrations));
  for i:=0 to High(SaltConcentrations) do
    BatchMasses[i]:=SaltConcentrations[i]*BatchVolume;
end;
```

### 8. RefinePrecision (hb_insprecision.pas)

```pascal
procedure THydroBuddy.RefinePrecision;
var i,j,idx:Integer; inc,resTol:Double; residuals:array of Double;
begin
  inc:=Power(10,-Form1.PrecisionDecimalPlaces);
  for i:=0 to High(BatchMasses) do BatchMasses[i]:=Round(BatchMasses[i]/inc)*inc;
  SetLength(residuals,Length(Elements));
  for j:=0 to High(Elements) do begin residuals[j]:=0; for i:=0 to High(SaltsList) do residuals[j]+=SaltsList[i].MassFractions[Elements[j]]*(BatchMasses[i]/BatchVolume); residuals[j]-=Targets[j]; end;
  idx:=0; for j:=1 to High(residuals) do if Abs(residuals[j])>Abs(residuals[idx]) then idx:=j;
  resTol:=Form1.MaxResidualTolerance;
  if Abs(residuals[idx])>resTol then begin // adjust
    var maxFrac:=0; var saltIdx:=0;
    for i:=0 to High(SaltsList) do if SaltsList[i].MassFractions[Elements[idx]]>maxFrac then begin maxFrac:=SaltsList[i].MassFractions[Elements[idx]]; saltIdx:=i; end;
    BatchMasses[saltIdx]-=Sign(residuals[idx])*inc;
  end;
end;
```

### 9. PrepareSolutionType (hb_stockanalysis.pas)

```pascal
procedure THydroBuddy.PrepareSolutionType;
begin
  case PrepType of
    ptBatch:       hb_stockanalysis.DoDirectBatch(BatchMasses,BatchVolume);
    ptSingleStock: hb_stockanalysis.DoSingleStock(BatchMasses,BatchVolume,StockVolume);
    ptTwoPart:     hb_stockanalysis.DoTwoPartStock;
    ptContinuous:  hb_stockanalysis.DoContinuousFeed;
  end;
end;
```

### 10. ConvertToCommercialMix (hb_commercialnutrient.pas)

```pascal
procedure THydroBuddy.ConvertToCommercialMix;
var dict:TDictionary<string,Double>; i:Integer; code:string;
begin
  dict:=TDictionary<string,Double>.Create; try
    for i:=0 to High(SaltsList) do begin code:=GetProductCode(SaltsList[i].Name); if not dict.ContainsKey(code) then dict.Add(code,0); dict[code]+=BatchMasses[i]; end;
    ProductList.Clear;
    for code in dict.Keys do ProductList.Add(TProductRecord.Create(code,dict[code],dict[code]*GetCostPerGram(code)));
  finally dict.Free; end;
end;
```

### 11. BreakdownPerSubstance (hb_persubstance.pas)

```pascal
procedure THydroBuddy.BreakdownPerSubstance;
var i,j:Integer; total:Double;
begin
  SetLength(ElementBreakdown,Length(Elements),Length(SaltsList));
  for i:=0 to High(SaltsList) do for j:=0 to High(Elements) do ElementBreakdown[j,i]:=SaltsList[i].MassFractions[Elements[j]]*SaltConcentrations[i];
  for j:=0 to High(Elements) do begin total:=0; for i:=0 to High(SaltsList) do total+=ElementBreakdown[j,i]; TotalElementConcentration[j]:=total; end;
end;
```

### 12. ComputePHEffect (hb_ph.pas)

```pascal
procedure THydroBuddy.ComputePHEffect;
var i:Integer; mmol,HA_tot,A_tot,pKa_eq:Double;
begin
  HA_tot:=0; A_tot:=0;
  for i:=0 to High(SaltsList) do begin mmol:=(SaltConcentrations[i]/SaltsList[i].MolecularWeight)*1000;
if IsAcidSalt(SaltsList[i].Name) then HA_tot+=mmol
  else
  if IsBaseSalt(SaltsList[i].Name) then A_tot+=mmol;
end;
  pKa_eq:=ComputeWeightedPKa(SaltsList,SaltConcentrations);
  if HA_tot>0 then PHResult:=pKa_eq+Log10(A_tot/HA_tot) else PHResult:=7.0;
  if Form1.rbAdjustPH.Checked then PHAdjustmentVolume:=(HA_tot-A_tot)*BatchVolume/Form1.CorrectionReagentConc;
end;
```

### 13. ComputeElementRatios (hb_ratios.pas)

```pascal
// Compute agronomic elemental ratios (e.g., N:K, Ca:Mg, Ca:K)
// Model: ratio_{A:B} = TotalElementConcentration[A] / TotalElementConcentration[B]
// Sequence:
// 1. Define list of ratio pairs in RatioDefinitions: each has Element1 and Element2
// 2. For each definition, look up concentration indices and perform division
// 3. Handle zero denominator by assigning zero or logging warning
procedure THydroBuddy.ComputeElementRatios;
var
  i: Integer;
  elemA, elemB: string;
  concA, concB: Double;
begin
  // Ensure RatioResults array matches number of definitions
  SetLength(RatioResults, Length(RatioDefinitions));

  // Loop through each defined ratio
  for i := 0 to High(RatioDefinitions) do
  begin
    elemA := RatioDefinitions[i].Element1;
    elemB := RatioDefinitions[i].Element2;

    // Lookup total concentrations in g/L
    concA := TotalElementConcentration[ElementIndex(elemA)];
    concB := TotalElementConcentration[ElementIndex(elemB)];

    // Calculate ratio, guard against division by zero
    if concB <> 0 then
      RatioResults[i] := concA / concB
    else
      RatioResults[i] := 0;
  end;
end;
```

**Functions & Models Used:**

- `ElementIndex(name: string): Integer` returns the index of an element in the Elements array.
- Ratios defined in a record array `RatioDefinitions: array of TRatioDefinition`, each with `Element1`, `Element2`.
- Simple arithmetic model dividing two concentrations (g/L).
- Zero-division handled gracefully by assigning 0.
  pascal
  procedure THydroBuddy.ComputeElementRatios;
  begin
  hb_ratios.CalculateRatios(TotalElementConcentration,RatioResults);
  end;

````

### 14. CompareWithStandards (hb_comparison.pas)
```pascal
// Compare computed ratios and concentrations against agronomic or industry standards
// Model: percent deviation = ((actual - target) / target) * 100
// Sequence:
// 1. Loop through each standard definition (for ratios or element conc.)
// 2. Retrieve measured value and standard target
// 3. Compute percentDeviation and flag if outside tolerance
// 4. Store results in DeviationResults array
procedure THydroBuddy.CompareWithStandards;
var
  i: Integer;
  actualValue, targetValue, percentDev: Double;
begin
  // Ensure DeviationResults matches StandardDefinitions length
  SetLength(DeviationResults, Length(StandardDefinitions));

  for i := 0 to High(StandardDefinitions) do
  begin
    // For ratio standards, compare RatioResults; for element conc., compare TotalElementConcentration
    if StandardDefinitions[i].IsRatio then
      actualValue := RatioResults[StandardDefinitions[i].Index]
    else
      actualValue := TotalElementConcentration[StandardDefinitions[i].Index];

    targetValue := StandardDefinitions[i].TargetValue;   // e.g. desired N:K = 3.0

    // Avoid division by zero
    if targetValue <> 0 then
      percentDev := ((actualValue - targetValue) / targetValue) * 100
    else
      percentDev := 0;

    // Populate DeviationResults record
    DeviationResults[i].Name := StandardDefinitions[i].Name;
    DeviationResults[i].Actual := actualValue;
    DeviationResults[i].Target := targetValue;
    DeviationResults[i].PercentDeviation := percentDev;
    DeviationResults[i].WithinTolerance := Abs(percentDev) <= StandardDefinitions[i].TolerancePercent;
  end;
end;
````

**Functions & Models Used:**

- `StandardDefinitions: array of TStandardDefinition` with fields:
  - `Name`: descriptor (e.g., 'N:K Ratio', 'Ca Concentration')
  - `Index`: index into `RatioResults` or `TotalElementConcentration`
  - `IsRatio`: boolean flag
  - `TargetValue`: desired numeric target
  - `TolerancePercent`: allowable deviation
- **Percent deviation** model: \(\% = (actual - target)/target imes 100\).
- Flags out-of-spec values for UI highlighting or warnings.
  pascal
  procedure THydroBuddy.CompareWithStandards;
  begin
  hb_comparison.CheckAgainstTargets(RatioResults,DeviationResults);
  end;

````

### 15. PerformTissueAnalysis (hb_tissue_analysis.pas)
```pascal
// Predict plant tissue concentrations based on application rate and uptake coefficients
// Model: C_tissue[j] = R_application[j] (g element/ha) × u_j (dimensionless uptake coefficient)
// Sequence:
// 1. Read application rate R_application[j] from UI or target formulation (g/ha per element)
// 2. Multiply by uptake coefficient u_j for each element j
// 3. Store result in TissueResults[j] (g element per kg biomass or as defined)
procedure THydroBuddy.PerformTissueAnalysis;
var
  j: Integer;
begin
  // Ensure TissueResults is sized to number of elements
  SetLength(TissueResults, Length(Elements));

  // Compute tissue concentration for each element
  for j := 0 to High(Elements) do
  begin
    // Application rate for element j in g/ha
    var appRate := Form1.ApplicationRate[Elements[j]];
    // Uptake coefficient (unitless or g tissue per g applied)
    var uptakeCoef := UptakeCoefficients[Elements[j]];
    // Tissue concentration result
    TissueResults[j] := appRate * uptakeCoef;
  end;
end;
````

**Functions & Models Used:**

- `ApplicationRate: TDictionary<string, Double>` maps each element to its applied mass (g/ha).
- `UptakeCoefficients: TDictionary<string, Double>` provides species-specific uptake factors.
- Linear uptake model: tissue conc = application rate × uptake coefficient.
  pascal
  procedure THydroBuddy.PerformTissueAnalysis;
  begin
  hb_tissue_analysis.PredictTissueLevels(Form1.ApplicationRate,UptakeCoefficients,TissueResults);
  end;

````

### 16. UI Update & Reporting
```pascal
// Refresh UI with new data and generate final reports
// Sequence:
// 1. Populate grid controls with raw and computed arrays
// 2. Render charts using tachartlazaruspkg components
// 3. Enable export buttons (CSV, PDF) and prepare datasets
procedure THydroBuddy.UpdateGridsAndCharts;
begin
  // 1. Update salt mass grid: shows BatchMasses per salt
  Form1.SaltGrid.RowCount := Length(SaltsList) + 1;
  for var i := 0 to High(SaltsList) do
  begin
    Form1.SaltGrid.Cells[0, i+1] := SaltsList[i].Name;
    Form1.SaltGrid.Cells[1, i+1] := FormatFloat('0.00', BatchMasses[i]) + ' g';
  end;

  // 2. Update element breakdown grid: TotalElementConcentration
  Form1.ElementGrid.RowCount := Length(Elements) + 1;
  for var j := 0 to High(Elements) do
  begin
    Form1.ElementGrid.Cells[0, j+1] := Elements[j];
    Form1.ElementGrid.Cells[1, j+1] := FormatFloat('0.00', TotalElementConcentration[j]) + ' g/L';
  end;

  // 3. Render charts
  // e.g., Bar chart of salt masses
  Form1.ChartSaltMass.Series[0].Clear;
  for var i := 0 to High(SaltsList) do
    Form1.ChartSaltMass.Series[0].Add(BatchMasses[i], SaltsList[i].Name);

  // e.g., Pie chart of element distribution
  Form1.ChartElementDist.Series[0].Clear;
  for var j := 0 to High(Elements) do
    Form1.ChartElementDist.Series[0].Add(TotalElementConcentration[j], Elements[j]);
end;

procedure THydroBuddy.ShowSummaryReport;
begin
  // 1. Assemble report text
  var rpt := TStringList.Create;
  try
    rpt.Add('HydroBuddy Summary Report');
    rpt.Add('------------------------');
    rpt.Add(Format('Batch Volume: %.2f L', [BatchVolume]));
    rpt.Add(' ');
    rpt.Add('Salt Masses:');
    for var i := 0 to High(SaltsList) do
      rpt.Add(Format('%s: %.2f g', [SaltsList[i].Name, BatchMasses[i]]));
    rpt.Add(' ');
    rpt.Add('Element Concentrations (g/L):');
    for var j := 0 to High(Elements) do
      rpt.Add(Format('%s: %.2f', [Elements[j], TotalElementConcentration[j]]));
    rpt.Add(' ');
    rpt.Add(Format('Estimated Cost: QAR %.2f', [CalcTotalCost]));
    rpt.Add(Format('pH Adjustment Volume: %.2f mL', [PHAdjustmentVolume]));

    // 2. Display in memo or export
    Form1.MemoReport.Lines := rpt;
    Form1.ShowReportDialog;

    // 3. Enable export buttons
    Form1.btnExportCSV.Enabled := True;
    Form1.btnExportPDF.Enabled := True;
  finally
    rpt.Free;
  end;
end;
````

\*End of comprehensive Pascal workflow.\*\*

# Now the DB I/O

All of the _.dbf_ I/O in HydroBuddy lives in the “load” routines at the very start of your workflow, and in the startup‐time file checks that point the app at your database folder. Here’s exactly where and how:

---

## A. Startup‐Time DB Handling

1. **`CheckDatabaseFiles`** (in your `.lpr` / `HB_Main.pas` before any forms show)

   - Verifies existence of

     ```
     Form1.formulations_db
     Form1.water_quality_db
     Form1.substances_db
     Form1.substances_used_db
     Form1.tissue_analysis_db
     ```

   - If missing, pops up the **Select Directory** dialog, then prepends that folder path to each name and rechecks.
   - If still missing, aborts with an error.

2. **`AssignValues`** (immediately after `CheckDatabaseFiles`)

   - Opens the water‐quality DBF and copies out the “Default = 1” record into your Form6 controls, so that your **LoadWaterQuality** routine has a backing store to read from.

---

## B. Section 1: Load & Validate All Inputs

These four routines each hit one (or two) DB tables:

1. **`LoadWaterQuality`**

   - **Unit**: `hb_waterquality.pas`
   - **DBF**: `waterquality_*.dbf`
   - **Models**: reads all ionic concentrations into the vector $\mathbf{W}$.
   - **Option**: manual vs. “Load from DB” is driven by your radio‐button/UI flag; if the user has selected “draw from DB,” this routine queries the TDbf, otherwise it simply parses the Edit-boxes.

2. **`LoadAvailableSalts`**

   - **Unit**: `hb_load_salts.pas`
   - **DBF**: `formulations_*.dbf`
   - **Models**: for each salt row reads molecular weight $M_i$ and element‐per‐mole fields to compute the fraction matrix $e_{j,i}$.

3. **`LoadCustomSalts`**

   - **Unit**: `hb_newcustomsalt.pas`
   - **DBF**: user-maintained table (same schema as formulations) where the app stores any new salts the user has added. Merges those rows onto the end of your salt arrays.

4. **`LoadTargetFormulation`**

   - **Unit**: `hb_datasetname.pas` (or wherever you handle named formulations)
   - **DBF**: `formulations_*.dbf` again, but reading from a “saved target” table when the user picks a pre-saved recipe.
   - **Option**: manual vs. “Draw from DB” radio; if “DB,” it loads the selected record into your $T_j$ vector, otherwise it grabs values from the Edit-boxes.

---

## C. Integration Points & Options

- Every one of these `Load…` routines checks a boolean or radio‐group on your main form to see if it should pull from a DBF or from the on‐screen fields.
- The very first thing your app does (in `Program … begin`) is run `CheckDatabaseFiles` → `AssignValues` → `UpdateComboBoxes`, so by the time you ever click **Calculate**, all your DB paths are set and any “Load from DB” flags have already populated the UI controls.
- Changing the database folder (via **File → Set DB Folder…**) simply re-runs `CheckDatabaseFiles` and then calls `AssignValues` again so your next calculation uses the new data.

---

### Quick Summary Table

| Section                   | Unit                   | DBF Table            | Manual vs. DB Option |
| ------------------------- | ---------------------- | -------------------- | -------------------- |
| Startup file check        | `HB_Main.pas`          | all five .dbf names  | N/A                  |
| 1.1 LoadWaterQuality      | `hb_waterquality.pas`  | `waterquality_*.dbf` | radio-button         |
| 1.2 LoadAvailableSalts    | `hb_load_salts.pas`    | `formulations_*.dbf` | always DB            |
| 1.3 LoadCustomSalts       | `hb_newcustomsalt.pas` | custom_salts.dbf     | always DB            |
| 1.4 LoadTargetFormulation | `hb_datasetname.pas`   | `formulations_*.dbf` | radio-button         |

Every subsequent step (stoichiometry, solving, reporting) works off the in-memory arrays you populated here.

# how HydroBuddy handles “manual vs. DB” loading for **Water Quality** and **Target Formulation**, including:

1. **Which DBF fields** it reads
2. **Which UI controls** (flags) govern each mode
3. **Pseudo-code** showing the branching logic

---

## A. Water‐Quality Loading

### 1. DBF Schema (`waterquality_*.dbf`)

Each record has these key fields (all concentrations in g/L):

```pascal
  Name        : String;       // profile name (e.g. “Tap Water”)
  Default     : Integer;      // 1 = this is the default profile
  N_NO3       : Float;        // “N (NO3-)”
  N_NH4       : Float;        // “N (NH4+)”
  P           : Float;        // phosphorus
  K           : Float;        // potassium
  Mg          : Float;
  Ca          : Float;
  S           : Float;        // sulfur
  Fe, Mn, Zn,
  B, Cu, Si,
  Mo, Na, Cl  : Float;        // other ions
```

### 2. UI Flag (on **Form6**, “Water Quality” tab)

- **`rgWQMode: TRadioGroup`**

  - `Items[0] = 'Manual Input'`
  - `Items[1] = 'Load from Database'`

### 3. Branching Logic in `LoadWaterQuality`

```pascal
procedure TForm6.LoadWaterQuality;
var
  MyDbf: TDbf;
begin
  if rgWQMode.ItemIndex = 1 then begin
    // — MODE: Load from DB —
    MyDbf := TDbf.Create(nil);
    try
      MyDbf.FilePathFull := WaterQualityDBPath;
      MyDbf.TableName     := ExtractFileName(WaterQualityDBPath);
      MyDbf.Open;
      MyDbf.FindFirst;
      while not MyDbf.EOF do
      begin
        if MyDbf.FieldByName('Default').AsInteger = 1 then
        begin
          // copy into UI fields
          edtNO3.Text  := MyDbf.FieldByName('N_NO3').AsString;
          edtNH4.Text  := MyDbf.FieldByName('N_NH4').AsString;
          edtP.Text    := MyDbf.FieldByName('P').AsString;
          // …and so on for K, Mg, Ca, S, Fe, Mn, Zn, B, Cu, Si, Mo, Na, Cl
          Break;
        end;
        MyDbf.Next;
      end;
    finally
      MyDbf.Free;
    end;
  end
  else begin
    // — MODE: Manual Input —
    // Values are already in the edtNO3..edtCl edit controls
    // You may optionally validate here (e.g. check for numeric & ≥0)
  end;
end;
```

---

## B. Target‐Formulation Loading

### 1. DBF Schema (`formulations_*.dbf`)

Each saved formulation record typically has:

```pascal
  Name       : String;   // e.g. “Balanced NPK”
  Default    : Integer;  // optional flag for default recipe
  N_Total    : Float;    // total N (g/L)
  P          : Float;
  K          : Float;
  Mg, Ca, S, // …and any other elements you support
  Fe, Mn, Zn,
  B, Cu, Si,
  Mo         : Float;
```

### 2. UI Flag (on **Form1**, “Formulation” tab)

- **`rgFormMode: TRadioGroup`**

  - `Items[0] = 'Manual Input'`
  - `Items[1] = 'Load Saved Formulation'`

- **`cmbSavedFormulas: TComboBox`**

  - Populated at startup with `Formulation.Name` values

### 3. Branching Logic in `LoadTargetFormulation`

```pascal
procedure TForm1.LoadTargetFormulation;
var
  MyDbf: TDbf;
begin
  if rgFormMode.ItemIndex = 1 then begin
    // — MODE: Load Saved Formulation from DB —
    MyDbf := TDbf.Create(nil);
    try
      MyDbf.FilePathFull := FormulationsDBPath;
      MyDbf.TableName     := ExtractFileName(FormulationsDBPath);
      MyDbf.Open;
      MyDbf.FindFirst;
      while not MyDbf.EOF do
      begin
        if MyDbf.FieldByName('Name').AsString = cmbSavedFormulas.Text then
        begin
          // copy each element into UI
          edtN.Text  := MyDbf.FieldByName('N_Total').AsString;
          edtP.Text  := MyDbf.FieldByName('P').AsString;
          edtK.Text  := MyDbf.FieldByName('K').AsString;
          // …and so on for Mg, Ca, S, Fe, Mn, Zn, B, Cu, Si, Mo
          Break;
        end;
        MyDbf.Next;
      end;
    finally
      MyDbf.Free;
    end;
  end
  else begin
    // — MODE: Manual Input —
    // Values live in the edtN..edtMo edit controls
  end;
end;
```

---

## C. Where & When

1. **Startup**

   - `CheckDatabaseFiles` ensures the `.dbf` paths are valid.
   - `AssignValues` may pre‐populate the UI with defaults.

2. **Before Calculation**

   - **Form6.LoadWaterQuality** is called from `PerformCalculation` (Section 1.1).
   - **Form1.LoadTargetFormulation** is called next (Section 1.4).

3. **User Switches Mode**

   - Toggling the radio group instantly changes which branch takes effect on the next **Calculate** run.

---

### Summary

| Load Step             | DBF File             | UI Control                       | Field Key Examples   |
| --------------------- | -------------------- | -------------------------------- | -------------------- |
| **Water Quality**     | `waterquality_*.dbf` | `rgWQMode`                       | `N_NO3`, `Ca`, `Cl`  |
| **Saved Formulation** | `formulations_*.dbf` | `rgFormMode`, `cmbSavedFormulas` | `N_Total`, `K`, `Si` |

Each routine checks its radio‐group flag first, then either:

- **Opens** the TDbf, **scans** for the matching record, **copies** fields into the edit controls
- **Or** skips DB entirely and leaves the user‐entered values in place.

Let me know if you’d like to see the exact code for your version of `CheckDatabaseFiles` or how the `ExtractFilePath(Application.ExeName)` logic builds those `FilePathFull` strings!

# SQL Migration Considerations

The migration from DBF to SQL (PostgreSQL) is appropriate, but I'd suggest a few additional considerations:

1. Schema design - The current DBF schema has relatively simple tables. When migrating to PostgreSQL:

Normalize the data more thoroughly (especially salt compositions)
Add proper foreign key relationships
Consider using schemas to organize different aspects (user data vs. calculation data) 2. Migration scripts - Ensure these are well-tested and idempotent to prevent data corruption

3. Data validation - Include validation during migration to ensure data quality/consistency

4. Performance considerations

Add appropriate indexes for query patterns
Consider materialized views for common calculations

# Potential Challenges

1. Calculation accuracy - Ensuring the TypeScript/Python implementation matches the Pascal calculations exactly

2. State management complexity - Hydroponics calculations involve many interrelated parameters; careful state design will be crucial

3. Performance on low-end devices - Modern web frameworks can be resource-intensive; optimize carefully

4. Migration of user data - Plan for how existing users will transition their data
