import React, { useEffect, useState } from 'react';
import axios from 'axios';

interface WaterElement {
  elementSymbol: string;
  concentration: number;
}

interface WaterProfile {
  id: string;
  name: string;
  description?: string;
  ec: number;
  ph: number;
  elements: WaterElement[];
}

const WaterProfilesTable: React.FC = () => {
  const [waterProfiles, setWaterProfiles] = useState<WaterProfile[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedProfile, setSelectedProfile] = useState<WaterProfile | null>(null);

  useEffect(() => {
    const fetchWaterProfiles = async () => {
      try {
        setLoading(true);
        const response = await axios.get('http://localhost:3001/api/water-profiles?includeElements=true');
        setWaterProfiles(response.data);
        setError(null);
      } catch (err) {
        setError('Failed to fetch water profiles. Please try again later.');
        console.error('Error fetching water profiles:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchWaterProfiles();
  }, []);

  const handleProfileClick = (profile: WaterProfile) => {
    setSelectedProfile(profile);
  };

  if (loading) {
    return <div className="loading">Loading water profiles...</div>;
  }

  if (error) {
    return <div className="error">{error}</div>;
  }

  return (
    <div className="water-profiles-container">
      <h2>Water Profiles</h2>
      
      <div className="profiles-grid">
        <div className="profiles-list">
          <table>
            <thead>
              <tr>
                <th>Name</th>
                <th>EC (mS/cm)</th>
                <th>pH</th>
              </tr>
            </thead>
            <tbody>
              {waterProfiles.map((profile) => (
                <tr 
                  key={profile.id} 
                  onClick={() => handleProfileClick(profile)}
                  className={selectedProfile?.id === profile.id ? 'selected' : ''}
                >
                  <td>{profile.name}</td>
                  <td>{profile.ec.toFixed(2)}</td>
                  <td>{profile.ph.toFixed(2)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {selectedProfile && (
          <div className="profile-details">
            <h3>{selectedProfile.name}</h3>
            {selectedProfile.description && (
              <p><strong>Description:</strong> {selectedProfile.description}</p>
            )}
            <p><strong>EC:</strong> {selectedProfile.ec.toFixed(2)} mS/cm</p>
            <p><strong>pH:</strong> {selectedProfile.ph.toFixed(2)}</p>
            
            <h4>Element Composition</h4>
            <table>
              <thead>
                <tr>
                  <th>Element</th>
                  <th>Concentration (ppm)</th>
                </tr>
              </thead>
              <tbody>
                {selectedProfile.elements.map((elem) => (
                  <tr key={elem.elementSymbol}>
                    <td>{elem.elementSymbol}</td>
                    <td>{elem.concentration.toFixed(2)}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default WaterProfilesTable;
