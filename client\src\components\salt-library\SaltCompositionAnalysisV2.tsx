import React, { useEffect, useState } from 'react';
import { 
  <PERSON>, 
  Card, 
  CardContent, 
  CardHeader, 
  Grid, 
  Typography, 
  useTheme,
  Paper,
  Tooltip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  IconButton,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Snackbar,
  Alert
} from '@mui/material';
import {
  ELEMENTS,
  ELEMENT_CATEGORIES,
  groupElementsByCategory,
  formatNPKString
} from '../../utils/elementCalculations';
import { exportToCsv, exportToJson } from '../../utils/exportUtils';
import { 
  PieChart, 
  Pie, 
  Cell, 
  ResponsiveContainer, 
  Sector, 
  Legend,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip
} from 'recharts';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import CodeIcon from '@mui/icons-material/Code';
import TableRowsIcon from '@mui/icons-material/TableRows';
import InfoIcon from '@mui/icons-material/Info';

interface ElementComposition {
  symbol: string;
  percentage: number;
  name?: string;
  color?: string;
}

interface SaltCompositionProps {
  saltName: string;
  formula?: string;
  elements: ElementComposition[];
  density?: number;
  solubility?: number;
  molecularWeight?: number;
}

// Element color mapping
const ELEMENT_COLORS = {
  N: '#3498db',   // Nitrogen - blue
  P: '#f39c12',   // Phosphorus - orange
  K: '#9b59b6',   // Potassium - purple
  Ca: '#e74c3c',  // Calcium - red
  Mg: '#2ecc71',  // Magnesium - green
  S: '#f1c40f',   // Sulfur - yellow
  Fe: '#e67e22',  // Iron - rust
  Mn: '#95a5a6',  // Manganese - gray
  Zn: '#1abc9c',  // Zinc - teal
  Cu: '#34495e',  // Copper - dark blue
  B: '#7f8c8d',   // Boron - dark gray
  Mo: '#d35400',  // Molybdenum - dark orange
  Cl: '#16a085',  // Chlorine - green blue
  Na: '#27ae60',  // Sodium - light green
  Si: '#8e44ad',  // Silicon - dark purple
  Co: '#c0392b',  // Cobalt - dark red
  // Default color for other elements
  default: '#bdc3c7' // light gray
};

const SaltCompositionAnalysisV2: React.FC<SaltCompositionProps> = ({ 
  saltName,
  formula,
  elements,
  density,
  solubility,
  molecularWeight
}) => {
  const theme = useTheme();
  const [activeIndex, setActiveIndex] = useState<number | undefined>(undefined);
  const [chartData, setChartData] = useState<ElementComposition[]>([]);
  const [chartView, setChartView] = useState<'pie' | 'bar'>('pie');
  
  // Export menu state
  const [exportMenuAnchor, setExportMenuAnchor] = useState<null | HTMLElement>(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error'>('success');

  // Prepare chart data
  useEffect(() => {
    // Sort elements by percentage (descending)
    const sortedElements = [...elements].sort((a, b) => b.percentage - a.percentage);
    
    // Assign colors to elements
    const coloredElements = sortedElements.map(elem => ({
      ...elem,
      color: (ELEMENT_COLORS as any)[elem.symbol] || ELEMENT_COLORS.default
    }));
    
    setChartData(coloredElements);
  }, [elements]);

  const onPieEnter = (_: any, index: number) => {
    setActiveIndex(index);
  };
  
  const onPieLeave = () => {
    setActiveIndex(undefined);
  };

  // Custom active shape for pie chart
  const renderActiveShape = (props: any) => {
    const { cx, cy, innerRadius, outerRadius, startAngle, endAngle, fill, payload, value } = props;
    
    return (
      <g>
        <Sector
          cx={cx}
          cy={cy}
          innerRadius={innerRadius}
          outerRadius={outerRadius + 10}
          startAngle={startAngle}
          endAngle={endAngle}
          fill={fill}
        />
        <Sector
          cx={cx}
          cy={cy}
          startAngle={startAngle}
          endAngle={endAngle}
          innerRadius={outerRadius + 15}
          outerRadius={outerRadius + 18}
          fill={fill}
        />
        <text x={cx} y={cy} textAnchor="middle" dominantBaseline="central">
          <tspan x={cx} y={cy-15} textAnchor="middle" fill="#333" fontWeight="bold">
            {payload.symbol}
          </tspan>
          <tspan x={cx} y={cy+15} textAnchor="middle" fontSize="14" fill="#333">
            {value.toFixed(1)}%
          </tspan>
        </text>
      </g>
    );
  };
  // Prepare export data
  const prepareExportData = () => {
    // Element data with categories
    const elementData = chartData.map(elem => {
      const category = Object.keys(ELEMENT_CATEGORIES).find(
        cat => ELEMENTS[elem.symbol]?.category === ELEMENT_CATEGORIES[cat as keyof typeof ELEMENT_CATEGORIES]
      ) || 'OTHER';
      
      return {
        name: elem.name || '',
        symbol: elem.symbol,
        percentage: elem.percentage,
        category
      };
    });
    
    // Salt information
    const saltInfo = {
      name: saltName,
      formula: formula || null,
      molecularWeight: molecularWeight || null,
      density: density || null,
      solubility: solubility || null,
      npk: formatNPKString(chartData)
    };
    
    return { saltInfo, elementData };
  };
  // Handle export menu
  const handleExportMenuOpen = (event: React.MouseEvent<HTMLButtonElement>) => {
    setExportMenuAnchor(event.currentTarget);
  };
  
  const handleExportMenuClose = () => {
    setExportMenuAnchor(null);
  };
  
  const showNotification = (message: string, severity: 'success' | 'error' = 'success') => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setSnackbarOpen(true);
  };
  
  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  // Export data as CSV
  const exportToCSV = () => {
    try {
      const { saltInfo, elementData } = prepareExportData();
      
      // Create table headers and rows
      const headers = ['Property', 'Value'];
      
      // Add salt information rows
      const saltInfoRows = [
        ['Salt Name', saltInfo.name],
        ['Formula', saltInfo.formula || 'N/A'],
        ['Molecular Weight', saltInfo.molecularWeight ? `${saltInfo.molecularWeight} g/mol` : 'N/A'],
        ['Density', saltInfo.density ? `${saltInfo.density} g/cm³` : 'N/A'],
        ['Solubility', saltInfo.solubility ? `${saltInfo.solubility} g/L at 20°C` : 'N/A'],
        ['N-P-K', saltInfo.npk],
        [''] // Empty row as separator
      ];
      
      // Add element data
      const elementHeaders = ['Element', 'Symbol', 'Percentage (%)', 'Category'];
      const elementRows = elementData.map(elem => [
        elem.name,
        elem.symbol,
        elem.percentage.toFixed(2),
        elem.category
      ]);
      
      // Export using utility function
      exportToCsv(headers, saltInfoRows, `${saltName}_composition`);
      handleExportMenuClose();
      showNotification('Salt composition exported as CSV successfully');
    } catch (error) {
      console.error('Error exporting to CSV:', error);
      showNotification('Failed to export salt composition as CSV', 'error');
    }
  };
  
  // Export data as JSON
  const exportToJSON = () => {
    try {
      const { saltInfo, elementData } = prepareExportData();
      const exportData = {
        salt: saltInfo,
        elements: elementData,
        exportDate: new Date().toISOString()
      };
      
      exportToJson(exportData, `${saltName}_composition`);
      handleExportMenuClose();
      showNotification('Salt composition exported as JSON successfully');
    } catch (error) {
      console.error('Error exporting to JSON:', error);
      showNotification('Failed to export salt composition as JSON', 'error');
    }
  };

  return (
    <Card>      <CardHeader 
        title={`${saltName} Composition Analysis`} 
        subheader={formula ? `Chemical Formula: ${formula}` : 'Element Breakdown'}
        action={
          <>
            <Button 
              startIcon={<FileDownloadIcon />} 
              onClick={handleExportMenuOpen}
              variant="outlined"
              size="small"
              aria-controls="export-menu"
              aria-haspopup="true"
            >
              Export Data
            </Button>
            <Menu
              id="export-menu"
              anchorEl={exportMenuAnchor}
              keepMounted
              open={Boolean(exportMenuAnchor)}
              onClose={handleExportMenuClose}
            >
              <MenuItem onClick={exportToCSV}>
                <ListItemIcon>
                  <TableRowsIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText primary="Export as CSV" />
              </MenuItem>
              <MenuItem onClick={exportToJSON}>
                <ListItemIcon>
                  <CodeIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText primary="Export as JSON" />
              </MenuItem>
            </Menu>
          </>
        }
      />
      <CardContent>
        <Grid container spacing={3}>
          {/* Visualization Section */}
          <Grid size={{ xs: 12, md: 6 }}>
            <Box sx={{ height: 350, width: '100%' }}>
              <Box sx={{ mb: 2, display: 'flex', justifyContent: 'center', gap: 2 }}>
                <Button 
                  variant={chartView === 'pie' ? 'contained' : 'outlined'} 
                  size="small"
                  onClick={() => setChartView('pie')}
                >
                  Pie Chart
                </Button>
                <Button 
                  variant={chartView === 'bar' ? 'contained' : 'outlined'} 
                  size="small"
                  onClick={() => setChartView('bar')}
                >
                  Bar Chart
                </Button>
              </Box>

              <ResponsiveContainer width="100%" height="100%">
                {chartView === 'pie' ? (
                  <PieChart>
                    <Pie
                      data={chartData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      activeIndex={activeIndex}
                      activeShape={renderActiveShape}
                      onMouseEnter={onPieEnter}
                      onMouseLeave={onPieLeave}
                      dataKey="percentage"
                      nameKey="symbol"
                      innerRadius={60}
                      outerRadius={80}
                    >
                      {chartData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Legend />
                  </PieChart>
                ) : (
                  <BarChart
                    data={chartData}
                    layout="vertical"
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" domain={[0, 'dataMax + 5']} />
                    <YAxis 
                      dataKey="symbol" 
                      type="category" 
                      width={40} 
                    />
                    <RechartsTooltip 
                      formatter={(value: any, name: any, props: any) => {
                        const entry = props.payload;
                        return [`${value}%`, entry.name || entry.symbol];
                      }}
                    />
                    <Bar dataKey="percentage" fill="#8884d8">
                      {chartData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Bar>
                  </BarChart>
                )}
              </ResponsiveContainer>
            </Box>
          </Grid>

          {/* Composition Table Section */}
          <Grid size={{ xs: 12, md: 6 }}>
            <Paper elevation={1} sx={{ p: 2 }}>
              <Typography variant="subtitle1" gutterBottom>
                Element Composition
              </Typography>
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Element</TableCell>
                      <TableCell>Symbol</TableCell>
                      <TableCell align="right">Percentage (%)</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {chartData.map((element) => (
                      <TableRow key={element.symbol}>
                        <TableCell>{element.name || 'Unknown'}</TableCell>
                        <TableCell>
                          <Box 
                            component="span" 
                            sx={{ 
                              display: 'inline-block',
                              width: 16,
                              height: 16,
                              borderRadius: '50%',
                              bgcolor: element.color,
                              mr: 1,
                              verticalAlign: 'middle'
                            }} 
                          />
                          {element.symbol}
                        </TableCell>
                        <TableCell align="right">{element.percentage.toFixed(2)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
              
              {/* Salt Properties Section */}
              {(molecularWeight || density || solubility) && (
                <Box sx={{ mt: 3 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Salt Properties
                  </Typography>
                  <Grid container spacing={2}>
                    {molecularWeight && (
                      <Grid size={{ xs: 12, sm: 4 }}>
                        <Paper elevation={1} sx={{ p: 2, textAlign: 'center' }}>
                          <Typography variant="body2" color="text.secondary">
                            Molecular Weight
                          </Typography>
                          <Typography variant="h6">
                            {molecularWeight.toFixed(2)} g/mol
                          </Typography>
                        </Paper>
                      </Grid>
                    )}
                    
                    {density && (
                      <Grid size={{ xs: 12, sm: 4 }}>
                        <Paper elevation={1} sx={{ p: 2, textAlign: 'center' }}>
                          <Typography variant="body2" color="text.secondary">
                            Density
                          </Typography>
                          <Typography variant="h6">
                            {density.toFixed(2)} g/cm³
                          </Typography>
                        </Paper>
                      </Grid>
                    )}
                    
                    {solubility && (
                      <Grid size={{ xs: 12, sm: 4 }}>
                        <Paper elevation={1} sx={{ p: 2, textAlign: 'center' }}>
                          <Typography variant="body2" color="text.secondary">
                            Solubility (20°C)
                          </Typography>
                          <Typography variant="h6">
                            {solubility.toFixed(1)} g/L
                          </Typography>
                        </Paper>
                      </Grid>
                    )}
                  </Grid>
                </Box>
              )}
              
              {/* Stats Section */}
              <Box sx={{ mt: 3 }}>
                <Grid container spacing={2}>
                  <Grid size={{ xs: 6 }}>
                    <Paper elevation={1} sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="body2" color="text.secondary">
                        Primary Element
                      </Typography>
                      <Typography variant="h6">
                        {chartData.length > 0 ? `${chartData[0].symbol} (${chartData[0].percentage.toFixed(1)}%)` : 'N/A'}
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid size={{ xs: 6 }}>
                    <Paper elevation={1} sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="body2" color="text.secondary">
                        Total Elements
                      </Typography>
                      <Typography variant="h6">
                        {chartData.length}
                      </Typography>
                    </Paper>
                  </Grid>
                </Grid>
              </Box>
            </Paper>
          </Grid>
          
          {/* Nutrient Classification */}
          <Grid size={{ xs: 12 }}>
            <Paper elevation={1} sx={{ p: 2, mt: 1 }}>
              <Typography variant="subtitle1" gutterBottom>
                Nutrient Classification
              </Typography>
              <Grid container spacing={2}>                {/* Import elementCalculations utility and use groupElementsByCategory for more accurate categorization */}
                {/* Macronutrients */}
                {chartData.some(e => ELEMENTS[e.symbol]?.category === ELEMENT_CATEGORIES.MACRONUTRIENT) && (
                  <Grid size={{ xs: 12, sm: 4 }}>
                    <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="body2" color="text.secondary">
                        Macronutrients (N-P-K)
                      </Typography>
                      <Typography variant="h6">
                        {formatNPKString(chartData)}
                      </Typography>
                    </Paper>
                  </Grid>
                )}
                
                {/* Secondary Nutrients */}
                {chartData.some(e => ELEMENTS[e.symbol]?.category === ELEMENT_CATEGORIES.SECONDARY) && (
                  <Grid size={{ xs: 12, sm: 4 }}>
                    <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="body2" color="text.secondary">
                        Secondary Nutrients
                      </Typography>
                      <Typography variant="body1">
                        {chartData
                          .filter(e => ELEMENTS[e.symbol]?.category === ELEMENT_CATEGORIES.SECONDARY)
                          .map(e => `${e.symbol}: ${e.percentage.toFixed(1)}%`)
                          .join(', ')}
                      </Typography>
                    </Paper>
                  </Grid>
                )}
                
                {/* Micronutrients */}
                {chartData.some(e => ELEMENTS[e.symbol]?.category === ELEMENT_CATEGORIES.MICRONUTRIENT) && (
                  <Grid size={{ xs: 12, sm: 4 }}>
                    <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="body2" color="text.secondary">
                        Micronutrients
                      </Typography>
                      <Typography variant="body1">
                        {chartData
                          .filter(e => ELEMENTS[e.symbol]?.category === ELEMENT_CATEGORIES.MICRONUTRIENT)
                          .map(e => `${e.symbol}: ${e.percentage.toFixed(1)}%`)
                          .join(', ')}
                      </Typography>
                    </Paper>
                  </Grid>
                )}
              </Grid>
            </Paper>
          </Grid>
        </Grid>      </CardContent>
      
      {/* Feedback Notifications */}
      <Snackbar 
        open={snackbarOpen} 
        autoHideDuration={4000} 
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert 
          onClose={handleSnackbarClose} 
          severity={snackbarSeverity}
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Card>
  );
};

export default SaltCompositionAnalysisV2;
