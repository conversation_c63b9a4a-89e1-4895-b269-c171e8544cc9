import { Request, Response } from 'express';
import { Salt } from '../models/salts';

// Get all salts
export const getAllSalts = async (req: Request, res: Response) => {
    try {
        const salts = await Salt.find();
        res.status(200).json(salts);
    } catch (error) {
        res.status(500).json({ message: 'Error retrieving salts', error });
    }
};

// Get a single salt by ID
export const getSaltById = async (req: Request, res: Response) => {
    const { id } = req.params;
    try {
        const salt = await Salt.findById(id);
        if (!salt) {
            return res.status(404).json({ message: 'Salt not found' });
        }
        res.status(200).json(salt);
    } catch (error) {
        res.status(500).json({ message: 'Error retrieving salt', error });
    }
};

// Create a new salt
export const createSalt = async (req: Request, res: Response) => {
    const newSalt = new Salt(req.body);
    try {
        const savedSalt = await newSalt.save();
        res.status(201).json(savedSalt);
    } catch (error) {
        res.status(400).json({ message: 'Error creating salt', error });
    }
};

// Update a salt by ID
export const updateSalt = async (req: Request, res: Response) => {
    const { id } = req.params;
    try {
        const updatedSalt = await Salt.findByIdAndUpdate(id, req.body, { new: true });
        if (!updatedSalt) {
            return res.status(404).json({ message: 'Salt not found' });
        }
        res.status(200).json(updatedSalt);
    } catch (error) {
        res.status(400).json({ message: 'Error updating salt', error });
    }
};

// Delete a salt by ID
export const deleteSalt = async (req: Request, res: Response) => {
    const { id } = req.params;
    try {
        const deletedSalt = await Salt.findByIdAndDelete(id);
        if (!deletedSalt) {
            return res.status(404).json({ message: 'Salt not found' });
        }
        res.status(204).send();
    } catch (error) {
        res.status(500).json({ message: 'Error deleting salt', error });
    }
};