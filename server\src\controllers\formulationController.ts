import { Request, Response } from 'express';
import { prisma } from '../app';

export const FormulationController = {
  // Get all formulations
  getAllFormulations: async (req: Request, res: Response) => {
    try {
      const { includeDetails } = req.query;
      let formulations;
      
      if (includeDetails === 'true') {
        formulations = await prisma.formulation.findMany({
          include: {
            waterProfile: {
              include: {
                elements: true
              }
            },
            salts: {
              include: {
                salt: {
                  include: {
                    elements: {
                      include: {
                        element: true
                      }
                    }
                  }
                }
              }
            },
            targetElements: {
              include: {
                element: true
              }
            }
          }
        });
      } else {
        formulations = await prisma.formulation.findMany({
          include: {
            waterProfile: true
          }
        });
      }
      
      return res.status(200).json(formulations);
    } catch (error) {
      console.error(`Error fetching formulations: ${error}`);
      return res.status(500).json({ error: 'Failed to fetch formulations' });
    }
  },

  // Get formulation by ID
  getFormulationById: async (req: Request, res: Response) => {
    const { id } = req.params;
    const { includeDetails } = req.query;
    
    try {
      let formulation;
      
      if (includeDetails === 'true') {
        formulation = await prisma.formulation.findUnique({
          where: { id },
          include: {
            waterProfile: {
              include: {
                elements: true
              }
            },
            salts: {
              include: {
                salt: {
                  include: {
                    elements: {
                      include: {
                        element: true
                      }
                    }
                  }
                }
              }
            },
            targetElements: {
              include: {
                element: true
              }
            }
          }
        });
      } else {
        formulation = await prisma.formulation.findUnique({
          where: { id },
          include: {
            waterProfile: true
          }
        });
      }
      
      if (!formulation) {
        return res.status(404).json({ error: 'Formulation not found' });
      }
      
      return res.status(200).json(formulation);
    } catch (error) {
      console.error(`Error fetching formulation: ${error}`);
      return res.status(500).json({ error: 'Failed to fetch formulation' });
    }
  },

  // Create new formulation
  createFormulation: async (req: Request, res: Response) => {
    const { 
      name, 
      description, 
      waterProfileId, 
      targetEc, 
      targetPh, 
      volume, 
      volumeUnit, 
      salts, 
      targetElements 
    } = req.body;
    
    if (!name || !waterProfileId || !volume) {
      return res.status(400).json({ 
        error: 'Formulation name, water profile, and volume are required' 
      });
    }
    
    try {
      // Check if water profile exists
      const waterProfile = await prisma.waterProfile.findUnique({
        where: { id: waterProfileId }
      });
      
      if (!waterProfile) {
        return res.status(404).json({ error: 'Water profile not found' });
      }
      
      // Start a transaction to create the formulation and its relations
      const result = await prisma.$transaction(async (prisma) => {
        // Create the formulation
        const formulation = await prisma.formulation.create({
          data: {
            name,
            description,
            waterProfileId,
            targetEc: targetEc ? parseFloat(targetEc) : null,
            targetPh: targetPh ? parseFloat(targetPh) : null,
            volume: parseFloat(volume),
            volumeUnit: volumeUnit || 'L'
          }
        });
        
        // Add salts if provided
        if (salts && Array.isArray(salts) && salts.length > 0) {
          for (const saltItem of salts) {
            const { saltId, amount, unit } = saltItem;
            
            // Check if salt exists
            const salt = await prisma.salt.findUnique({
              where: { id: saltId }
            });
            
            if (!salt) {
              throw new Error(`Salt with ID ${saltId} not found`);
            }
            
            if (amount === undefined || isNaN(parseFloat(amount))) {
              throw new Error('Valid amount is required for each salt in formulation');
            }
            
            // Create the formulation-salt relation
            await prisma.formulationSalt.create({
              data: {
                formulationId: formulation.id,
                saltId,
                amount: parseFloat(amount),
                unit: unit || 'g'
              }
            });
          }
        }
        
        // Add target elements if provided
        if (targetElements && Array.isArray(targetElements) && targetElements.length > 0) {
          for (const elemItem of targetElements) {
            const { elementId, concentration, unit } = elemItem;
            
            // Check if element exists
            const element = await prisma.element.findUnique({
              where: { id: elementId }
            });
            
            if (!element) {
              throw new Error(`Element with ID ${elementId} not found`);
            }
            
            if (concentration === undefined || isNaN(parseFloat(concentration))) {
              throw new Error('Valid concentration is required for each target element');
            }
            
            // Create the target element relation
            await prisma.targetElement.create({
              data: {
                formulationId: formulation.id,
                elementId,
                concentration: parseFloat(concentration),
                unit: unit || 'ppm'
              }
            });
          }
        }
        
        // Return the formulation with its details
        return prisma.formulation.findUnique({
          where: { id: formulation.id },
          include: {
            waterProfile: true,
            salts: {
              include: {
                salt: true
              }
            },
            targetElements: {
              include: {
                element: true
              }
            }
          }
        });
      });
      
      return res.status(201).json(result);    } catch (error) {
      console.error(`Error creating formulation: ${error}`);
      return res.status(500).json({ error: `Failed to create formulation: ${error instanceof Error ? error.message : String(error)}` });
    }
  },

  // Update formulation
  updateFormulation: async (req: Request, res: Response) => {
    const { id } = req.params;
    const { 
      name, 
      description, 
      waterProfileId, 
      targetEc, 
      targetPh, 
      volume, 
      volumeUnit, 
      salts, 
      targetElements 
    } = req.body;
    
    try {
      // Check if formulation exists
      const existingFormulation = await prisma.formulation.findUnique({
        where: { id }
      });
      
      if (!existingFormulation) {
        return res.status(404).json({ error: 'Formulation not found' });
      }
      
      // Check if water profile exists if provided
      if (waterProfileId) {
        const waterProfile = await prisma.waterProfile.findUnique({
          where: { id: waterProfileId }
        });
        
        if (!waterProfile) {
          return res.status(404).json({ error: 'Water profile not found' });
        }
      }
      
      // Parse numeric values
      let parsedTargetEc = undefined;
      let parsedTargetPh = undefined;
      let parsedVolume = undefined;
        if (targetEc !== undefined) {
        parsedTargetEc = targetEc ? parseFloat(targetEc) : null;
        if (targetEc && isNaN(parseFloat(targetEc))) {
          return res.status(400).json({ error: 'Target EC must be a valid number' });
        }
      }
        if (targetPh !== undefined) {
        parsedTargetPh = targetPh ? parseFloat(targetPh) : null;
        if (targetPh && isNaN(parseFloat(targetPh))) {
          return res.status(400).json({ error: 'Target pH must be a valid number' });
        }
      }
      
      if (volume !== undefined) {
        parsedVolume = parseFloat(volume);
        if (isNaN(parsedVolume)) {
          return res.status(400).json({ error: 'Volume must be a valid number' });
        }
      }
      
      // Update the formulation in a transaction
      const result = await prisma.$transaction(async (prisma) => {
        // Update the formulation properties
        const formulation = await prisma.formulation.update({
          where: { id },
          data: {
            ...(name && { name }),
            ...(description !== undefined && { description }),
            ...(waterProfileId && { waterProfileId }),
            ...(parsedTargetEc !== undefined && { targetEc: parsedTargetEc }),
            ...(parsedTargetPh !== undefined && { targetPh: parsedTargetPh }),
            ...(parsedVolume !== undefined && { volume: parsedVolume }),
            ...(volumeUnit && { volumeUnit })
          }
        });
        
        // Update salts if provided
        if (salts && Array.isArray(salts)) {
          // Remove existing formulation-salt relations
          await prisma.formulationSalt.deleteMany({
            where: { formulationId: id }
          });
          
          // Add new salts
          for (const saltItem of salts) {
            const { saltId, amount, unit } = saltItem;
            
            // Check if salt exists
            const salt = await prisma.salt.findUnique({
              where: { id: saltId }
            });
            
            if (!salt) {
              throw new Error(`Salt with ID ${saltId} not found`);
            }
            
            if (amount === undefined || isNaN(parseFloat(amount))) {
              throw new Error('Valid amount is required for each salt in formulation');
            }
            
            // Create the formulation-salt relation
            await prisma.formulationSalt.create({
              data: {
                formulationId: formulation.id,
                saltId,
                amount: parseFloat(amount),
                unit: unit || 'g'
              }
            });
          }
        }
        
        // Update target elements if provided
        if (targetElements && Array.isArray(targetElements)) {
          // Remove existing target elements
          await prisma.targetElement.deleteMany({
            where: { formulationId: id }
          });
          
          // Add new target elements
          for (const elemItem of targetElements) {
            const { elementId, concentration, unit } = elemItem;
            
            // Check if element exists
            const element = await prisma.element.findUnique({
              where: { id: elementId }
            });
            
            if (!element) {
              throw new Error(`Element with ID ${elementId} not found`);
            }
            
            if (concentration === undefined || isNaN(parseFloat(concentration))) {
              throw new Error('Valid concentration is required for each target element');
            }
            
            // Create the target element relation
            await prisma.targetElement.create({
              data: {
                formulationId: formulation.id,
                elementId,
                concentration: parseFloat(concentration),
                unit: unit || 'ppm'
              }
            });
          }
        }
        
        // Return the formulation with its details
        return prisma.formulation.findUnique({
          where: { id: formulation.id },
          include: {
            waterProfile: true,
            salts: {
              include: {
                salt: true
              }
            },
            targetElements: {
              include: {
                element: true
              }
            }
          }
        });
      });
      
      return res.status(200).json(result);    } catch (error) {
      console.error(`Error updating formulation: ${error}`);
      return res.status(500).json({ error: `Failed to update formulation: ${error instanceof Error ? error.message : String(error)}` });
    }
  },

  // Delete formulation
  deleteFormulation: async (req: Request, res: Response) => {
    const { id } = req.params;
    
    try {
      // Use a transaction to delete formulation relations and the formulation
      await prisma.$transaction(async (prisma) => {
        // Delete formulation-salt relations
        await prisma.formulationSalt.deleteMany({
          where: { formulationId: id }
        });
        
        // Delete target elements
        await prisma.targetElement.deleteMany({
          where: { formulationId: id }
        });
        
        // Delete the formulation
        await prisma.formulation.delete({
          where: { id }
        });
      });
      
      return res.status(204).send();
    } catch (error) {
      console.error(`Error deleting formulation: ${error}`);
      return res.status(500).json({ error: 'Failed to delete formulation' });
    }
  },
  
  // Search formulations
  searchFormulations: async (req: Request, res: Response) => {
    const { term } = req.query;
    
    if (!term || typeof term !== 'string') {
      return res.status(400).json({ error: 'Search term is required' });
    }
    
    try {
      const formulations = await prisma.formulation.findMany({
        where: {
          OR: [
            { name: { contains: term } },
            { description: { contains: term } }
          ]
        },
        include: {
          waterProfile: true,
          salts: {
            include: {
              salt: true
            }
          },
          targetElements: {
            include: {
              element: true
            }
          }
        }
      });
      
      return res.status(200).json(formulations);
    } catch (error) {
      console.error(`Error searching formulations: ${error}`);
      return res.status(500).json({ error: 'Failed to search formulations' });
    }
  }
};
