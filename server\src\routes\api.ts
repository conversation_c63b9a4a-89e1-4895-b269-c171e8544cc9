import { Router } from 'express';
import waterQualityController from '../controllers/waterQuality';
import saltsController from '../controllers/salts';
import formulationController from '../controllers/formulation';
import calculationController from '../controllers/calculation';

const router = Router();

// Water Quality Routes
router.get('/water-quality', waterQualityController.getAll);
router.post('/water-quality', waterQualityController.create);
router.get('/water-quality/:id', waterQualityController.getById);
router.put('/water-quality/:id', waterQualityController.update);
router.delete('/water-quality/:id', waterQualityController.delete);

// Salts Routes
router.get('/salts', saltsController.getAll);
router.post('/salts', saltsController.create);
router.get('/salts/:id', saltsController.getById);
router.put('/salts/:id', saltsController.update);
router.delete('/salts/:id', saltsController.delete);

// Formulation Routes
router.get('/formulation', formulationController.getAll);
router.post('/formulation', formulationController.create);
router.get('/formulation/:id', formulationController.getById);
router.put('/formulation/:id', formulationController.update);
router.delete('/formulation/:id', formulationController.delete);

// Calculation Routes
router.post('/calculate', calculationController.calculate);

export default router;