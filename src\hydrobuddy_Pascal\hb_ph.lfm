object Form13: TForm13
  Left = 853
  Height = 143
  Top = 415
  Width = 353
  BorderStyle = bsToolWindow
  Caption = 'Alkalinity adjustment'
  ClientHeight = 143
  ClientWidth = 353
  FormStyle = fsStayOnTop
  Position = poMainFormCenter
  LCLVersion = '2.0.13.0'
  object Label18: TLabel
    Left = 25
    Height = 15
    Top = 24
    Width = 113
    Caption = 'Total Alkanility (ppm)'
    ParentColor = False
  end
  object Edit18: TEdit
    Left = 192
    Height = 23
    Top = 24
    Width = 144
    TabOrder = 0
    Text = '0'
  end
  object Button1: TButton
    Left = 25
    Height = 25
    Hint = 'This will add the nutrient contribution of the acid selected, in the amount needed to neutralize the total alkalinity of the source water'
    Top = 96
    Width = 311
    Caption = 'Add acid nutrient contribution to targets'
    OnClick = Button1Click
    ParentShowHint = False
    ShowHint = True
    TabOrder = 1
  end
  object Label1: TLabel
    Left = 25
    Height = 15
    Top = 56
    Width = 120
    Caption = 'Acid used to neutralize'
    ParentColor = False
  end
  object ComboBox1: TComboBox
    Left = 192
    Height = 23
    Top = 56
    Width = 144
    ItemHeight = 15
    ItemIndex = 0
    Items.Strings = (
      'Phosphoric acid'
      'Sulfuric acid'
      'Nitric acid'
    )
    OnChange = ComboBox1Change
    Style = csDropDownList
    TabOrder = 2
    Text = 'Phosphoric acid'
  end
end
