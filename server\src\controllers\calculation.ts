import { Request, Response } from 'express';
import { calculateStoichiometry, calculateScaling, calculatePrecision } from '../services';

export const performStoichiometry = (req: Request, res: Response) => {
    const { inputs } = req.body;
    try {
        const result = calculateStoichiometry(inputs);
        res.status(200).json(result);
    } catch (error) {
        res.status(500).json({ error: 'Error performing stoichiometry calculation' });
    }
};

export const performScaling = (req: Request, res: Response) => {
    const { inputs } = req.body;
    try {
        const result = calculateScaling(inputs);
        res.status(200).json(result);
    } catch (error) {
        res.status(500).json({ error: 'Error performing scaling calculation' });
    }
};

export const performPrecision = (req: Request, res: Response) => {
    const { inputs } = req.body;
    try {
        const result = calculatePrecision(inputs);
        res.status(200).json(result);
    } catch (error) {
        res.status(500).json({ error: 'Error performing precision calculation' });
    }
};