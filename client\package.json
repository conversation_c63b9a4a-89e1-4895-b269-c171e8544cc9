{"name": "hydrobuddy-pwa", "version": "1.0.0", "private": true, "scripts": {"start": "vite", "build": "vite build", "serve": "vite preview", "lint": "eslint . --ext .ts,.tsx", "test": "vitest", "dev": "vite", "preview": "vite preview", "type-check": "tsc --noEmit"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@reduxjs/toolkit": "^2.8.2", "axios": "^1.9.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-redux": "^9.2.0", "react-router-dom": "^6.30.1", "recharts": "^2.15.3", "redux": "^5.0.1"}, "devDependencies": {"@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.5.0", "eslint": "^9.27.0", "eslint-plugin-react": "^7.37.5", "typescript": "^5.8.3", "vite": "^6.3.5", "vitest": "^0.1.0"}}