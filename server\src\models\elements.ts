// filepath: /hydrobuddy-pwa/hydrobuddy-pwa/server/src/models/elements.ts

import { Schema, model } from 'mongoose';

const elementSchema = new Schema({
  name: {
    type: String,
    required: true,
    unique: true,
  },
  atomicNumber: {
    type: Number,
    required: true,
  },
  symbol: {
    type: String,
    required: true,
    unique: true,
  },
  molarMass: {
    type: Number,
    required: true,
  },
  description: {
    type: String,
    required: false,
  },
});

const Element = model('Element', elementSchema);

export default Element;