/**
 * Unit Conversion Utility
 * 
 * This module provides functions for converting between different units of measurement
 * commonly used in hydroponics calculations.
 */

// Molar masses for different elements (g/mol)
export const molarMasses = {
  N: 14.007,   // Nitrogen
  NH4: 14.007, // Ammonium (represented as N)
  NO3: 14.007, // Nitrate (represented as N)
  P: 30.974,   // Phosphorus
  K: 39.098,   // Potassium
  Mg: 24.305,  // Magnesium
  Ca: 40.078,  // Calcium
  S: 32.066,   // Sulfur
  Fe: 55.845,  // Iron
  Mn: 54.938,  // Manganese
  Zn: 65.409,  // Zinc
  B: 10.811,   // Boron
  Cu: 63.546,  // Copper
  Si: 28.086,  // Silicon
  Mo: 95.94,   // Molybdenum
  Na: 22.990,  // Sodium
  Cl: 35.453,  // Chlorine
  CO3: 60.008, // Carbonate
  HCO3: 61.016 // Bicarbonate
};

// Oxidation states/equivalents for elements (for meq/L calculations)
export const equivalents = {
  N: 1,
  NH4: 1,
  NO3: 1,
  P: 3,
  K: 1,
  Mg: 2,
  Ca: 2,
  S: 2,
  Fe: 2,
  Mn: 2,
  Zn: 2,
  B: 1,
  Cu: 2,
  Si: 4,
  Mo: 6,
  Na: 1,
  Cl: 1,
  CO3: 2,
  HCO3: 1
};

// Oxide conversion factors (for converting between elemental and oxide forms)
export const oxideFactors = {
  P2O5: 2.29,  // P to P2O5
  K2O: 1.20,   // K to K2O
  SiO2: 2.14,  // Si to SiO2
  CaO: 1.40,   // Ca to CaO
  MgO: 1.66    // Mg to MgO
};

// Specific conductivity factors for ions (μS/cm per mg/L at 25°C)
export const conductivityFactors = {
  Ca: 0.048,
  Mg: 0.086,
  K: 0.073, 
  Na: 0.047,
  NH4: 0.074,
  NO3: 0.065,
  SO4: 0.080,
  Cl: 0.076,
  HCO3: 0.082,
  CO3: 0.085,
  P: 0.034,    // As phosphate
  Fe: 0.054,
  Mn: 0.055,
  Zn: 0.047,
  Cu: 0.053,
  B: 0.111,
  Mo: 0.031
};

// PPM conversion factors (1 EC = x ppm)
export const ppmScales = {
  '500': 500,  // EC 1.0 = 500 ppm (often used in US)
  '640': 640,  // EC 1.0 = 640 ppm (often used in Europe) 
  '700': 700   // EC 1.0 = 700 ppm (Hanna meters)
};

/**
 * Converts volume to cubic meters
 * @param value The volume value to convert
 * @param unit The unit of the volume (L, gal, mL, fl_oz)
 * @returns Volume in cubic meters
 */
export function volumeToCubicMeters(value: number, unit: 'L' | 'gal' | 'mL' | 'fl_oz'): number {
  switch (unit) {
    case 'L':
      return value / 1000; // L to m³
    case 'gal':
      return value * 3.78541178 / 1000; // US gallon to m³
    case 'mL':
      return value / 1000000; // mL to m³
    case 'fl_oz':
      return value * 0.0000295735; // US fluid ounce to m³
    default:
      throw new Error(`Unknown volume unit: ${unit}`);
  }
}

/**
 * Converts volume between different units
 * @param value The volume value to convert
 * @param fromUnit Source unit
 * @param toUnit Target unit
 * @returns Converted volume
 */
export function convertVolume(
  value: number, 
  fromUnit: 'L' | 'gal' | 'mL' | 'fl_oz',
  toUnit: 'L' | 'gal' | 'mL' | 'fl_oz'
): number {
  if (fromUnit === toUnit) return value;
  
  // Convert to cubic meters first as intermediate step
  const cubicMeters = volumeToCubicMeters(value, fromUnit);
  
  // Convert from cubic meters to target unit
  switch (toUnit) {
    case 'L':
      return cubicMeters * 1000;
    case 'gal':
      return cubicMeters * 1000 / 3.78541178;
    case 'mL':
      return cubicMeters * 1000000;
    case 'fl_oz':
      return cubicMeters / 0.0000295735;
    default:
      throw new Error(`Unknown volume unit: ${toUnit}`);
  }
}

/**
 * Converts weight between different units
 * @param value The weight value to convert
 * @param fromUnit Source unit
 * @param toUnit Target unit
 * @returns Converted weight
 */
export function convertWeight(
  value: number,
  fromUnit: 'g' | 'kg' | 'mg' | 'lb' | 'oz',
  toUnit: 'g' | 'kg' | 'mg' | 'lb' | 'oz'
): number {
  if (fromUnit === toUnit) return value;
  
  // Convert to grams first as intermediate step
  let grams: number;
  switch (fromUnit) {
    case 'g':
      grams = value;
      break;
    case 'kg':
      grams = value * 1000;
      break;
    case 'mg':
      grams = value / 1000;
      break;
    case 'lb':
      grams = value * 453.59237;
      break;
    case 'oz':
      grams = value * 28.3495231;
      break;
    default:
      throw new Error(`Unknown weight unit: ${fromUnit}`);
  }
  
  // Convert from grams to target unit
  switch (toUnit) {
    case 'g':
      return grams;
    case 'kg':
      return grams / 1000;
    case 'mg':
      return grams * 1000;
    case 'lb':
      return grams / 453.59237;
    case 'oz':
      return grams / 28.3495231;
    default:
      throw new Error(`Unknown weight unit: ${toUnit}`);
  }
}

/**
 * Converts temperature between Celsius and Fahrenheit
 * @param value Temperature value
 * @param fromUnit Source temperature unit ('C' or 'F')
 * @param toUnit Target temperature unit ('C' or 'F')
 * @returns Converted temperature
 */
export function convertTemperature(
  value: number,
  fromUnit: 'C' | 'F',
  toUnit: 'C' | 'F'
): number {
  if (fromUnit === toUnit) return value;
  
  if (fromUnit === 'C' && toUnit === 'F') {
    return (value * 9/5) + 32;
  } else if (fromUnit === 'F' && toUnit === 'C') {
    return (value - 32) * 5/9;
  } else {
    throw new Error(`Unknown temperature unit conversion: ${fromUnit} to ${toUnit}`);
  }
}

/**
 * Converts concentration from ppm to other units
 * @param ppm Concentration in ppm (mg/L or g/m³)
 * @param element Element symbol
 * @param targetUnit Target unit ('ppm', 'M', 'mM', 'meq')
 * @returns Concentration in the target unit
 */
export function convertFromPpm(ppm: number, element: keyof typeof molarMasses, targetUnit: 'ppm' | 'M' | 'mM' | 'meq'): number {
  switch (targetUnit) {
    case 'ppm':
      return ppm; // No conversion needed
    case 'M':
      return ppm / (1000 * molarMasses[element]); // ppm to mol/L
    case 'mM':
      return ppm / molarMasses[element]; // ppm to mmol/L
    case 'meq':
      return ppm / (molarMasses[element] / equivalents[element as keyof typeof equivalents]); // ppm to meq/L
    default:
      throw new Error(`Unknown target unit: ${targetUnit}`);
  }
}

/**
 * Converts concentration to ppm from other units
 * @param value Concentration value
 * @param element Element symbol
 * @param sourceUnit Source unit ('ppm', 'M', 'mM', 'meq')
 * @returns Concentration in ppm (mg/L or g/m³)
 */
export function convertToPpm(value: number, element: keyof typeof molarMasses, sourceUnit: 'ppm' | 'M' | 'mM' | 'meq'): number {
  switch (sourceUnit) {
    case 'ppm':
      return value; // No conversion needed
    case 'M':
      return value * 1000 * molarMasses[element]; // mol/L to ppm
    case 'mM':
      return value * molarMasses[element]; // mmol/L to ppm
    case 'meq':
      return value * (molarMasses[element] / equivalents[element as keyof typeof equivalents]); // meq/L to ppm
    default:
      throw new Error(`Unknown source unit: ${sourceUnit}`);
  }
}

/**
 * Converts between elemental and oxide forms
 * @param value Value to convert
 * @param conversion Conversion type ('P_to_P2O5', 'P2O5_to_P', 'K_to_K2O', 'K2O_to_K', 'Si_to_SiO2', 'SiO2_to_Si')
 * @returns Converted value
 */
export function convertBetweenElementalAndOxide(value: number, conversion: 
  'P_to_P2O5' | 'P2O5_to_P' | 
  'K_to_K2O' | 'K2O_to_K' | 
  'Si_to_SiO2' | 'SiO2_to_Si' |
  'Ca_to_CaO' | 'CaO_to_Ca' |
  'Mg_to_MgO' | 'MgO_to_Mg'): number {
  switch (conversion) {
    case 'P_to_P2O5':
      return value * oxideFactors.P2O5;
    case 'P2O5_to_P':
      return value / oxideFactors.P2O5;
    case 'K_to_K2O':
      return value * oxideFactors.K2O;
    case 'K2O_to_K':
      return value / oxideFactors.K2O;
    case 'Si_to_SiO2':
      return value * oxideFactors.SiO2;
    case 'SiO2_to_Si':
      return value / oxideFactors.SiO2;
    case 'Ca_to_CaO':
      return value * oxideFactors.CaO;
    case 'CaO_to_Ca':
      return value / oxideFactors.CaO;
    case 'Mg_to_MgO':
      return value * oxideFactors.MgO;
    case 'MgO_to_Mg':
      return value / oxideFactors.MgO;
    default:
      throw new Error(`Unknown conversion: ${conversion}`);
  }
}

/**
 * Calculate electrical conductivity (EC) based on ion concentrations
 * @param ions Object with element symbols as keys and concentrations in ppm as values
 * @param unit Unit of the input concentrations ('ppm' or 'mM')
 * @returns EC value in mS/cm at 25°C
 */
export function calculateEC(
  ions: Partial<Record<keyof typeof conductivityFactors, number>>,
  unit: 'ppm' | 'mM' = 'ppm'
): number {
  let totalEC = 0;
  
  for (const [element, concentration] of Object.entries(ions)) {
    if (element in conductivityFactors) {
      const elementKey = element as keyof typeof conductivityFactors;
      
      // If unit is mM, convert to ppm first
      const ppmValue = unit === 'ppm' 
        ? concentration 
        : convertToPpm(concentration, element as keyof typeof molarMasses, 'mM');
      
      totalEC += ppmValue * conductivityFactors[elementKey];
    }
  }
  
  // Return EC in mS/cm (divide by 1000 to convert from μS/cm)
  return Number((totalEC / 1000).toFixed(2));
}

/**
 * Converts EC value to ppm using different conversion scales
 * @param ec EC value in mS/cm
 * @param scale PPM scale ('500', '640', '700')
 * @returns PPM value based on the selected scale
 */
export function ecToPpm(ec: number, scale: keyof typeof ppmScales = '500'): number {
  return ec * ppmScales[scale];
}

/**
 * Converts ppm value to EC using different conversion scales
 * @param ppm PPM value
 * @param scale PPM scale ('500', '640', '700')
 * @returns EC value in mS/cm
 */
export function ppmToEc(ppm: number, scale: keyof typeof ppmScales = '500'): number {
  return ppm / ppmScales[scale];
}

/**
 * Calculates water hardness in German degrees (dGH) based on Ca and Mg concentrations
 * @param ca Calcium concentration in ppm
 * @param mg Magnesium concentration in ppm
 * @returns Water hardness in German degrees (dGH)
 */
export function calculateDGH(ca: number, mg: number): number {
  // Convert Ca and Mg to mmol/L
  const caMmol = convertFromPpm(ca, 'Ca', 'mM');
  const mgMmol = convertFromPpm(mg, 'Mg', 'mM');
  
  // 1 dGH = 0.179 mmol/L of CaCO3 equivalent
  // Ca²⁺ and Mg²⁺ contribute to hardness with a 1:1 equivalence for CaCO3
  const hardnessMmol = caMmol + mgMmol;
  const dGH = hardnessMmol / 0.179;
  
  return Number(dGH.toFixed(1));
}

/**
 * Calculates carbonate hardness (KH) in German degrees (dKH) based on carbonate and bicarbonate
 * @param hco3 Bicarbonate concentration in ppm
 * @param co3 Carbonate concentration in ppm (optional)
 * @returns Carbonate hardness in German degrees (dKH)
 */
export function calculateDKH(hco3: number, co3: number = 0): number {
  // Convert HCO3- and CO3²- to meq/L
  const hco3Meq = convertFromPpm(hco3, 'HCO3', 'meq');
  const co3Meq = convertFromPpm(co3, 'CO3', 'meq');
  
  // 1 dKH = 0.357 meq/L of alkalinity
  // CO3²- counts double for alkalinity because it can neutralize 2 H+
  const alkalinityMeq = hco3Meq + (2 * co3Meq);
  const dKH = alkalinityMeq / 0.357;
  
  return Number(dKH.toFixed(1));
}
