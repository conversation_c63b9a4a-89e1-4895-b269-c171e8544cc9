import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface SaltResult {
  id: number;
  name: string;
  formula: string;
  weight: number;
  cost: number;
}

interface ElementalBreakdown {
  element: string;
  target: number;
  actual: number;
  unit: string;
  difference: number;
}

interface ResultsState {
  salts: SaltResult[];
  elementalBreakdown: ElementalBreakdown[];
  totalCost: number;
  notes: string;
  loading: boolean;
  error: string | null;
}

const initialState: ResultsState = {
  salts: [],
  elementalBreakdown: [],
  totalCost: 0,
  notes: '',
  loading: false,
  error: null,
};

const resultsSlice = createSlice({
  name: 'results',
  initialState,
  reducers: {
    setResults(state, action: PayloadAction<{
      salts: SaltResult[];
      elementalBreakdown: ElementalBreakdown[];
      totalCost: number;
    }>) {
      state.salts = action.payload.salts;
      state.elementalBreakdown = action.payload.elementalBreakdown;
      state.totalCost = action.payload.totalCost;
      state.loading = false;
      state.error = null;
    },
    setNotes(state, action: PayloadAction<string>) {
      state.notes = action.payload;
    },
    clearResults(state) {
      state.salts = [];
      state.elementalBreakdown = [];
      state.totalCost = 0;
      state.notes = '';
    },
    loadResultsStart(state) {
      state.loading = true;
      state.error = null;
    },
    loadResultsFailed(state, action: PayloadAction<string>) {
      state.loading = false;
      state.error = action.payload;
    },
  },
});

export const {
  setResults,
  setNotes,
  clearResults,
  loadResultsStart,
  loadResultsFailed,
} = resultsSlice.actions;

export default resultsSlice.reducer;
