import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import { 
  Card,
  Card<PERSON>ontent,
  CardHeader,
  Typography,
  Box,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  Divider,
  TableContainer, 
  Table, 
  TableHead, 
  TableBody, 
  TableRow, 
  TableCell,
  IconButton,
  Alert,
  Tooltip,
  Chip
} from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import CompareArrowsIcon from '@mui/icons-material/CompareArrows';
import { RootState } from '../../store/store';

interface WaterProfileData {
  name: string;
  ph: number;
  ec: number;
  tds: number;
  temperature: number;
  ca: number;
  mg: number;
  k: number;
  na: number;
  cl: number;
  so4: number;
  no3: number;
  hco3: number;
  co3: number;
}

interface WaterQualityComparisonProps {
  currentWaterData?: WaterProfileData;
}

const WaterQualityComparison: React.FC<WaterQualityComparisonProps> = ({ currentWaterData }) => {
  const profiles = useSelector((state: RootState) => state.waterQuality.profiles);
  const allProfiles = currentWaterData 
    ? [currentWaterData, ...profiles.filter(p => p.name !== currentWaterData.name)]
    : profiles;
  
  const [selectedProfile1, setSelectedProfile1] = useState<string>(
    allProfiles.length > 0 ? allProfiles[0].name : ''
  );
  const [selectedProfile2, setSelectedProfile2] = useState<string>(
    allProfiles.length > 1 ? allProfiles[1].name : ''
  );
  
  const profile1 = allProfiles.find(p => p.name === selectedProfile1);
  const profile2 = allProfiles.find(p => p.name === selectedProfile2);
  
  // Calculate percent difference between two values
  const calculateDifference = (value1: number, value2: number): { 
    diff: number; 
    percentDiff: number; 
    isSignificant: boolean;
  } => {
    const diff = value1 - value2;
    const absValue = Math.abs(diff);
    
    // Avoid division by zero
    let percentDiff = 0;
    if (value2 !== 0) {
      percentDiff = Math.round((absValue / value2) * 100);
    } else if (value1 !== 0) {
      percentDiff = 100; // If value2 is zero but value1 isn't, that's a 100% change
    }
    
    // Determine if the difference is significant based on context
    const isSignificant = percentDiff > 20;
    
    return { diff, percentDiff, isSignificant };
  };

  // Calculate water hardness for a profile
  const calculateDGH = (ca: number, mg: number): number => {
    return Math.round(((ca / 7.14) + (mg / 4.33)) * 10) / 10;
  };

  const calculateDKH = (hco3: number, co3: number): number => {
    return Math.round(((hco3 / 21.8) + (co3 / 10.9)) * 10) / 10;
  };
  
  // Get visual indicator for difference
  const getDifferenceIndicator = (diff: number, isSignificant: boolean) => {
    if (!isSignificant) return null;
    
    if (diff > 0) {
      return <Chip size="small" color="success" label={`+${diff}`} />;
    } else if (diff < 0) {
      return <Chip size="small" color="error" label={diff} />;
    }
    return null;
  };
  
  // No profiles available
  if (allProfiles.length < 2) {
    return (
      <Card>
        <CardHeader title="Water Profile Comparison" />
        <CardContent>
          <Alert severity="info">
            You need at least two water profiles to make a comparison. 
            Please create another water profile first.
          </Alert>
        </CardContent>
      </Card>
    );
  }
  
  return (
    <Card>
      <CardHeader 
        title="Water Profile Comparison" 
        subheader="Compare water quality parameters between profiles"
        action={
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Tooltip title="Compare multiple profiles to analyze differences in water quality parameters">
              <IconButton size="small">
                <InfoIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
        }
      />
      <CardContent>
        <Box sx={{ mb: 4 }}>
          <Grid container spacing={3}>
            <Grid xs={12} md={5}>
              <FormControl fullWidth>
                <InputLabel id="profile1-label">First Water Profile</InputLabel>
                <Select
                  labelId="profile1-label"
                  value={selectedProfile1}
                  label="First Water Profile"
                  onChange={(e) => setSelectedProfile1(e.target.value as string)}
                >
                  {allProfiles.map((profile) => (
                    <MenuItem 
                      key={`profile1-${profile.name}`} 
                      value={profile.name}
                      disabled={profile.name === selectedProfile2}
                    >
                      {profile.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid xs={12} md={2} sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <CompareArrowsIcon sx={{ fontSize: 40 }} />
              </Box>
            </Grid>
            
            <Grid xs={12} md={5}>
              <FormControl fullWidth>
                <InputLabel id="profile2-label">Second Water Profile</InputLabel>
                <Select
                  labelId="profile2-label"
                  value={selectedProfile2}
                  label="Second Water Profile"
                  onChange={(e) => setSelectedProfile2(e.target.value as string)}
                >
                  {allProfiles.map((profile) => (
                    <MenuItem 
                      key={`profile2-${profile.name}`} 
                      value={profile.name}
                      disabled={profile.name === selectedProfile1}
                    >
                      {profile.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </Box>
        
        {profile1 && profile2 && (
          <>
            <TableContainer component={Paper} sx={{ mb: 3 }}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Parameter</TableCell>
                    <TableCell>{profile1.name}</TableCell>
                    <TableCell>{profile2.name}</TableCell>
                    <TableCell>Difference</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  <TableRow>
                    <TableCell component="th" scope="row">pH</TableCell>
                    <TableCell>{profile1.ph}</TableCell>
                    <TableCell>{profile2.ph}</TableCell>
                    <TableCell>
                      {(() => {
                        const { diff, percentDiff, isSignificant } = calculateDifference(profile1.ph, profile2.ph);
                        return (
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography variant="body2">{diff.toFixed(1)}</Typography>
                            {getDifferenceIndicator(diff, isSignificant)}
                          </Box>
                        );
                      })()}
                    </TableCell>
                  </TableRow>
                  
                  <TableRow>
                    <TableCell component="th" scope="row">EC (mS/cm)</TableCell>
                    <TableCell>{profile1.ec}</TableCell>
                    <TableCell>{profile2.ec}</TableCell>
                    <TableCell>
                      {(() => {
                        const { diff, percentDiff, isSignificant } = calculateDifference(profile1.ec, profile2.ec);
                        return (
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography variant="body2">{diff.toFixed(2)}</Typography>
                            {getDifferenceIndicator(diff, isSignificant)}
                          </Box>
                        );
                      })()}
                    </TableCell>
                  </TableRow>
                  
                  <TableRow>
                    <TableCell component="th" scope="row">TDS (ppm)</TableCell>
                    <TableCell>{profile1.tds}</TableCell>
                    <TableCell>{profile2.tds}</TableCell>
                    <TableCell>
                      {(() => {
                        const { diff, percentDiff, isSignificant } = calculateDifference(profile1.tds, profile2.tds);
                        return (
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography variant="body2">{diff}</Typography>
                            {getDifferenceIndicator(diff, isSignificant)}
                          </Box>
                        );
                      })()}
                    </TableCell>
                  </TableRow>
                  
                  <TableRow>
                    <TableCell component="th" scope="row">Temperature (°C)</TableCell>
                    <TableCell>{profile1.temperature}</TableCell>
                    <TableCell>{profile2.temperature}</TableCell>
                    <TableCell>
                      {(() => {
                        const { diff, percentDiff, isSignificant } = calculateDifference(profile1.temperature, profile2.temperature);
                        return (
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography variant="body2">{diff}</Typography>
                            {getDifferenceIndicator(diff, isSignificant)}
                          </Box>
                        );
                      })()}
                    </TableCell>
                  </TableRow>
                  
                  <TableRow>
                    <TableCell component="th" scope="row">German Hardness (dGH)</TableCell>
                    <TableCell>{calculateDGH(profile1.ca, profile1.mg)}</TableCell>
                    <TableCell>{calculateDGH(profile2.ca, profile2.mg)}</TableCell>
                    <TableCell>
                      {(() => {
                        const dgh1 = calculateDGH(profile1.ca, profile1.mg);
                        const dgh2 = calculateDGH(profile2.ca, profile2.mg);
                        const { diff, percentDiff, isSignificant } = calculateDifference(dgh1, dgh2);
                        return (
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography variant="body2">{diff.toFixed(1)}</Typography>
                            {getDifferenceIndicator(diff, isSignificant)}
                          </Box>
                        );
                      })()}
                    </TableCell>
                  </TableRow>
                  
                  <TableRow>
                    <TableCell component="th" scope="row">Carbonate Hardness (dKH)</TableCell>
                    <TableCell>{calculateDKH(profile1.hco3, profile1.co3)}</TableCell>
                    <TableCell>{calculateDKH(profile2.hco3, profile2.co3)}</TableCell>
                    <TableCell>
                      {(() => {
                        const dkh1 = calculateDKH(profile1.hco3, profile1.co3);
                        const dkh2 = calculateDKH(profile2.hco3, profile2.co3);
                        const { diff, percentDiff, isSignificant } = calculateDifference(dkh1, dkh2);
                        return (
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography variant="body2">{diff.toFixed(1)}</Typography>
                            {getDifferenceIndicator(diff, isSignificant)}
                          </Box>
                        );
                      })()}
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </TableContainer>
            
            <Typography variant="h6" gutterBottom>
              Ion Composition Comparison (ppm)
            </Typography>
            
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
              <Paper sx={{ flex: '1 1 300px', p: 2 }}>
                <Typography variant="subtitle1" gutterBottom>Cations</Typography>
                <Divider sx={{ mb: 2 }} />
                <TableContainer>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Ion</TableCell>
                        <TableCell>{profile1.name}</TableCell>
                        <TableCell>{profile2.name}</TableCell>
                        <TableCell>Diff.</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {[
                        { label: 'Ca²⁺', key: 'ca' },
                        { label: 'Mg²⁺', key: 'mg' },
                        { label: 'K⁺', key: 'k' },
                        { label: 'Na⁺', key: 'na' }
                      ].map((ion) => (
                        <TableRow key={ion.key}>
                          <TableCell>{ion.label}</TableCell>
                          <TableCell>{(profile1 as any)[ion.key]}</TableCell>
                          <TableCell>{(profile2 as any)[ion.key]}</TableCell>
                          <TableCell>
                            {(() => {
                              const { diff, percentDiff, isSignificant } = calculateDifference(
                                (profile1 as any)[ion.key], 
                                (profile2 as any)[ion.key]
                              );
                              return (
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                  <Typography variant="body2">{diff.toFixed(1)}</Typography>
                                  {getDifferenceIndicator(diff, isSignificant)}
                                </Box>
                              );
                            })()}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Paper>
              
              <Paper sx={{ flex: '1 1 300px', p: 2 }}>
                <Typography variant="subtitle1" gutterBottom>Anions</Typography>
                <Divider sx={{ mb: 2 }} />
                <TableContainer>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Ion</TableCell>
                        <TableCell>{profile1.name}</TableCell>
                        <TableCell>{profile2.name}</TableCell>
                        <TableCell>Diff.</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {[
                        { label: 'Cl⁻', key: 'cl' },
                        { label: 'SO₄²⁻', key: 'so4' },
                        { label: 'NO₃⁻', key: 'no3' },
                        { label: 'HCO₃⁻', key: 'hco3' },
                        { label: 'CO₃²⁻', key: 'co3' }
                      ].map((ion) => (
                        <TableRow key={ion.key}>
                          <TableCell>{ion.label}</TableCell>
                          <TableCell>{(profile1 as any)[ion.key]}</TableCell>
                          <TableCell>{(profile2 as any)[ion.key]}</TableCell>
                          <TableCell>
                            {(() => {
                              const { diff, percentDiff, isSignificant } = calculateDifference(
                                (profile1 as any)[ion.key], 
                                (profile2 as any)[ion.key]
                              );
                              return (
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                  <Typography variant="body2">{diff.toFixed(1)}</Typography>
                                  {getDifferenceIndicator(diff, isSignificant)}
                                </Box>
                              );
                            })()}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Paper>
            </Box>
            
            <Box sx={{ mt: 3 }}>
              <Alert severity="info">
                <Typography variant="body2">
                  <strong>Interpretation:</strong> Significant differences in water parameters may require adjustments to your nutrient solution. 
                  Pay special attention to EC, pH, and key nutrient ions that show large variations.
                </Typography>
              </Alert>
            </Box>
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default WaterQualityComparison;
