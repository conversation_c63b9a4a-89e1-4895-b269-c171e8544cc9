// test-unit-conversion.ts
import { volumeToCubicMeters, convertFromPpm, convertToPpm, convertBetweenElementalAndOxide, convertTemperature, calculateEC, calculateDGH, calculateDKH } from './src/utils/unitConversion';
import * as solubility from './src/utils/solubilityCalculation';

console.log('Testing Unit Conversion Utility');
console.log('===============================');

// Test volume conversions
console.log('\nVolume Conversions:');
console.log(`10 L = ${volumeToCubicMeters(10, 'L')} m³`);
console.log(`10 gal = ${volumeToCubicMeters(10, 'gal')} m³`);

// Test concentration conversions
console.log('\nConcentration Conversions:');
console.log(`100 ppm N = ${convertFromPpm(100, 'N', 'mM')} mM N`);
console.log(`100 ppm Ca = ${convertFromPpm(100, 'Ca', 'mM')} mM Ca`);
console.log(`5 mM K = ${convertToPpm(5, 'K', 'mM')} ppm K`);
console.log(`2 mM Mg = ${convertToPpm(2, 'Mg', 'mM')} ppm Mg`);

// Test oxide conversions
console.log('\nOxide Conversions:');
console.log(`10 P = ${convertBetweenElementalAndOxide(10, 'P_to_P2O5')} P₂O₅`);
console.log(`10 P₂O₅ = ${convertBetweenElementalAndOxide(10, 'P2O5_to_P')} P`);
console.log(`10 K = ${convertBetweenElementalAndOxide(10, 'K_to_K2O')} K₂O`);
console.log(`10 K₂O = ${convertBetweenElementalAndOxide(10, 'K2O_to_K')} K`);

// Test temperature conversions
console.log('\nTemperature Conversions:');
console.log(`20°C = ${convertTemperature(20, 'C', 'F')}°F`);
console.log(`68°F = ${convertTemperature(68, 'F', 'C')}°C`);

// Test EC and hardness calculations
console.log('\nEC and Hardness Calculations:');
console.log(`EC from 1 mM Ca, 1 mM Mg, 1 mM K, 1 mM NO3 = ${calculateEC({
  Ca: 1, 
  Mg: 1, 
  K: 1, 
  NO3: 1
}, 'mM')} mS/cm`);
console.log(`dGH from 40 ppm Ca, 10 ppm Mg = ${calculateDGH(40, 10)} dGH`);
console.log(`dKH from 60 ppm HCO3 = ${calculateDKH(60, 0)} dKH`);

// Test solubility calculations
console.log('\nSolubility Calculations:');
console.log(`KNO3 solubility at 20°C = ${solubility.calculateSolubilityAtTemperature('k_no3', 20)} g/100mL`);
console.log(`KNO3 solubility at 25°C = ${solubility.calculateSolubilityAtTemperature('k_no3', 25)} g/100mL (interpolated)`);
console.log(`Is 40 g/100mL of KNO3 soluble at 25°C? ${solubility.isSoluble('k_no3', 40, 25)}`);
console.log(`Maximum safe KNO3 concentration at 25°C = ${solubility.getMaxSafeConcentration('k_no3', 25)} g/100mL`);
console.log(`Minimum temperature to dissolve 50 g/100mL of KNO3 = ${solubility.findMinimumTemperatureForSolubility('k_no3', 50)}°C`);
