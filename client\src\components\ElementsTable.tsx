import React, { useEffect, useState } from 'react';
import axios from 'axios';

interface Element {
  id: string;
  symbol: string;
  name: string;
  atomicNumber: number;
  atomicWeight: number;
  category: string;
  description?: string;
}

const ElementsTable: React.FC = () => {
  const [elements, setElements] = useState<Element[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchElements = async () => {
      try {
        setLoading(true);
        const response = await axios.get('http://localhost:3001/api/elements');
        setElements(response.data);
        setError(null);
      } catch (err) {
        setError('Failed to fetch elements. Please try again later.');
        console.error('Error fetching elements:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchElements();
  }, []);

  if (loading) {
    return <div className="loading">Loading elements...</div>;
  }

  if (error) {
    return <div className="error">{error}</div>;
  }

  return (
    <div className="elements-table">
      <h2>Elements</h2>
      <table>
        <thead>
          <tr>
            <th>Symbol</th>
            <th>Name</th>
            <th>Atomic Number</th>
            <th>Atomic Weight</th>
            <th>Category</th>
          </tr>
        </thead>
        <tbody>
          {elements.map((element) => (
            <tr key={element.id}>
              <td>{element.symbol}</td>
              <td>{element.name}</td>
              <td>{element.atomicNumber}</td>
              <td>{element.atomicWeight}</td>
              <td>{element.category}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default ElementsTable;
