import axios from 'axios';

const API_URL = '/api/calculate';

export const calculatePrecision = async (data) => {
    try {
        const response = await axios.post(`${API_URL}/precision`, data);
        return response.data;
    } catch (error) {
        throw new Error('Error calculating precision: ' + error.message);
    }
};

export const calculateStockPlan = async (data) => {
    try {
        const response = await axios.post(`${API_URL}/stock-plan`, data);
        return response.data;
    } catch (error) {
        throw new Error('Error calculating stock plan: ' + error.message);
    }
};

export const calculateCommercialMix = async (data) => {
    try {
        const response = await axios.post(`${API_URL}/commercial-mix`, data);
        return response.data;
    } catch (error) {
        throw new Error('Error calculating commercial mix: ' + error.message);
    }
};

export const calculateBreakdown = async (data) => {
    try {
        const response = await axios.post(`${API_URL}/breakdown`, data);
        return response.data;
    } catch (error) {
        throw new Error('Error calculating breakdown: ' + error.message);
    }
};

export const calculatePH = async (data) => {
    try {
        const response = await axios.post(`${API_URL}/ph`, data);
        return response.data;
    } catch (error) {
        throw new Error('Error calculating pH: ' + error.message);
    }
};

export const calculateRatios = async (data) => {
    try {
        const response = await axios.post(`${API_URL}/ratios`, data);
        return response.data;
    } catch (error) {
        throw new Error('Error calculating ratios: ' + error.message);
    }
};