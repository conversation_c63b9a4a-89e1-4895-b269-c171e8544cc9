/**
 * Utility functions for element calculations in hydroponics applications.
 * Used by the Salt Composition Calculator and related components.
 */

interface Element {
  symbol: string;
  name: string;
  atomicWeight: number;
  category?: string;
}

interface ElementComposition {
  symbol: string;
  percentage: number;
  atomicWeight?: number;
}

// Element categories
export const ELEMENT_CATEGORIES = {
  MACRONUTRIENT: 'macronutrient',
  SECONDARY: 'secondary',
  MICRONUTRIENT: 'micronutrient',
  OTHER: 'other'
};

// Common elements in hydroponics with atomic weights
export const ELEMENTS: Record<string, Element> = {
  'N': { symbol: 'N', name: 'Nitrogen', atomicWeight: 14.0067, category: ELEMENT_CATEGORIES.MACRONUTRIENT },
  'P': { symbol: 'P', name: 'Phosphorus', atomicWeight: 30.9738, category: ELEMENT_CATEGORIES.MACRONUTRIENT },
  'K': { symbol: 'K', name: 'Potassium', atomicWeight: 39.0983, category: ELEMENT_CATEGORIES.MACRONUTRIENT },
  'Ca': { symbol: 'Ca', name: 'Calci<PERSON>', atomicWeight: 40.078, category: ELEMENT_CATEGORIES.SECONDARY },
  'Mg': { symbol: 'Mg', name: 'Magnesium', atomicWeight: 24.305, category: ELEMENT_CATEGORIES.SECONDARY },
  'S': { symbol: 'S', name: 'Sulfur', atomicWeight: 32.065, category: ELEMENT_CATEGORIES.SECONDARY },
  'Fe': { symbol: 'Fe', name: 'Iron', atomicWeight: 55.845, category: ELEMENT_CATEGORIES.MICRONUTRIENT },
  'Mn': { symbol: 'Mn', name: 'Manganese', atomicWeight: 54.938, category: ELEMENT_CATEGORIES.MICRONUTRIENT },
  'Zn': { symbol: 'Zn', name: 'Zinc', atomicWeight: 65.38, category: ELEMENT_CATEGORIES.MICRONUTRIENT },
  'Cu': { symbol: 'Cu', name: 'Copper', atomicWeight: 63.546, category: ELEMENT_CATEGORIES.MICRONUTRIENT },
  'B': { symbol: 'B', name: 'Boron', atomicWeight: 10.811, category: ELEMENT_CATEGORIES.MICRONUTRIENT },
  'Mo': { symbol: 'Mo', name: 'Molybdenum', atomicWeight: 95.96, category: ELEMENT_CATEGORIES.MICRONUTRIENT },
  'Cl': { symbol: 'Cl', name: 'Chlorine', atomicWeight: 35.453, category: ELEMENT_CATEGORIES.MICRONUTRIENT },
  'Na': { symbol: 'Na', name: 'Sodium', atomicWeight: 22.9897, category: ELEMENT_CATEGORIES.OTHER },
  'O': { symbol: 'O', name: 'Oxygen', atomicWeight: 15.9994, category: ELEMENT_CATEGORIES.OTHER },
  'H': { symbol: 'H', name: 'Hydrogen', atomicWeight: 1.00784, category: ELEMENT_CATEGORIES.OTHER },
  'C': { symbol: 'C', name: 'Carbon', atomicWeight: 12.0107, category: ELEMENT_CATEGORIES.OTHER },
  'Si': { symbol: 'Si', name: 'Silicon', atomicWeight: 28.0855, category: ELEMENT_CATEGORIES.OTHER },
  'Co': { symbol: 'Co', name: 'Cobalt', atomicWeight: 58.9332, category: ELEMENT_CATEGORIES.MICRONUTRIENT },
  'Ni': { symbol: 'Ni', name: 'Nickel', atomicWeight: 58.6934, category: ELEMENT_CATEGORIES.MICRONUTRIENT },
};

/**
 * Calculate molecular weight of a salt based on its element composition
 * @param elements Array of element compositions with percentages
 * @returns Total molecular weight or null if elements are missing data
 */
export const calculateMolecularWeight = (elements: ElementComposition[]): number | null => {
  if (!elements || elements.length === 0) {
    return null;
  }

  let totalWeight = 0;
  
  for (const elem of elements) {
    const elementInfo = ELEMENTS[elem.symbol];
    if (!elementInfo) {
      console.warn(`Element ${elem.symbol} not found in database`);
      return null;
    }
    
    // Add element's contribution to the total weight based on its percentage
    totalWeight += (elementInfo.atomicWeight * elem.percentage / 100);
  }
  
  return totalWeight;
};

/**
 * Calculate the stock solution concentration based on salt solubility
 * @param solubility Solubility in g/L at 20°C
 * @param volumeInLiters Volume of stock solution in liters
 * @returns Maximum concentration in grams
 */
export const calculateStockSolutionConcentration = (
  solubility: number, 
  volumeInLiters: number
): number => {
  return solubility * volumeInLiters;
};

/**
 * Group elements by their nutrient category
 * @param elements Array of element compositions
 * @returns Object with elements grouped by category
 */
export const groupElementsByCategory = (elements: ElementComposition[]): Record<string, ElementComposition[]> => {
  const result: Record<string, ElementComposition[]> = {
    [ELEMENT_CATEGORIES.MACRONUTRIENT]: [],
    [ELEMENT_CATEGORIES.SECONDARY]: [],
    [ELEMENT_CATEGORIES.MICRONUTRIENT]: [],
    [ELEMENT_CATEGORIES.OTHER]: [],
  };
  
  elements.forEach(elem => {
    const elementInfo = ELEMENTS[elem.symbol];
    if (elementInfo) {
      const category = elementInfo.category || ELEMENT_CATEGORIES.OTHER;
      result[category].push({
        ...elem,
        atomicWeight: elementInfo.atomicWeight
      });
    } else {
      result[ELEMENT_CATEGORIES.OTHER].push(elem);
    }
  });
  
  return result;
};

/**
 * Format NPK values from element composition
 * @param elements Array of element compositions
 * @returns NPK string in format "N-P-K"
 */
export const formatNPKString = (elements: ElementComposition[]): string => {
  const n = elements.find(e => e.symbol === 'N')?.percentage.toFixed(1) || '0.0';
  const p = elements.find(e => e.symbol === 'P')?.percentage.toFixed(1) || '0.0';
  const k = elements.find(e => e.symbol === 'K')?.percentage.toFixed(1) || '0.0';
  
  return `${n}-${p}-${k}`;
};

/**
 * Check if the total percentage of elements is valid (close to 100%)
 * @param elements Array of element compositions
 * @param tolerance Allowed deviation from 100% (default 5%)
 * @returns Boolean indicating if composition is valid
 */
export const isValidComposition = (elements: ElementComposition[], tolerance = 5): boolean => {
  if (!elements || elements.length === 0) return false;
  
  const total = elements.reduce((sum, elem) => sum + elem.percentage, 0);
  return Math.abs(total - 100) <= tolerance;
};

export default {
  calculateMolecularWeight,
  calculateStockSolutionConcentration,
  groupElementsByCategory,
  formatNPKString,
  isValidComposition,
  ELEMENTS,
  ELEMENT_CATEGORIES
};
