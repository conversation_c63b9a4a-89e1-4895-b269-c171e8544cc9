/**
 * Solubility calculation utilities
 * This module provides functions for calculating solubility of salts based on temperature
 */

/**
 * Interface for solubility data points
 * Each point represents solubility (g/100mL) at a specific temperature (°C)
 */
interface SolubilityDataPoint {
  temperature: number;
  solubility: number;
}

/**
 * Solubility data for common fertilizer salts
 * Data structure: { saltId: [{ temperature, solubility }, ...] }
 * Units: temperature in °C, solubility in g/100mL water
 */
export const solubilityData: Record<string, SolubilityDataPoint[]> = {
  // Calcium Nitrate [Ca(NO3)2]
  'ca_no3': [
    { temperature: 0, solubility: 102 },
    { temperature: 10, solubility: 113 },
    { temperature: 20, solubility: 121 },
    { temperature: 30, solubility: 129 },
    { temperature: 40, solubility: 138 },
    { temperature: 50, solubility: 151 },
    { temperature: 60, solubility: 165 },
    { temperature: 70, solubility: 181 },
    { temperature: 80, solubility: 199 },
    { temperature: 90, solubility: 217 },
    { temperature: 100, solubility: 244 }
  ],
  
  // Potassium Nitrate [KNO3]
  'k_no3': [
    { temperature: 0, solubility: 13.3 },
    { temperature: 10, solubility: 20.9 },
    { temperature: 20, solubility: 31.6 },
    { temperature: 30, solubility: 45.8 },
    { temperature: 40, solubility: 63.9 },
    { temperature: 50, solubility: 85.5 },
    { temperature: 60, solubility: 110.0 },
    { temperature: 70, solubility: 137.5 },
    { temperature: 80, solubility: 168.0 },
    { temperature: 90, solubility: 202.0 },
    { temperature: 100, solubility: 245.0 }
  ],
  
  // Magnesium Nitrate [Mg(NO3)2]
  'mg_no3': [
    { temperature: 0, solubility: 66.0 },
    { temperature: 10, solubility: 68.0 },
    { temperature: 20, solubility: 71.0 },
    { temperature: 30, solubility: 74.0 },
    { temperature: 40, solubility: 77.0 },
    { temperature: 50, solubility: 80.5 },
    { temperature: 60, solubility: 84.0 },
    { temperature: 70, solubility: 88.0 },
    { temperature: 80, solubility: 92.0 },
    { temperature: 90, solubility: 96.0 },
    { temperature: 100, solubility: 100.0 }
  ],
  
  // Potassium Sulfate [K2SO4]
  'k2_so4': [
    { temperature: 0, solubility: 7.4 },
    { temperature: 10, solubility: 9.3 },
    { temperature: 20, solubility: 11.1 },
    { temperature: 30, solubility: 12.9 },
    { temperature: 40, solubility: 14.6 },
    { temperature: 50, solubility: 16.3 },
    { temperature: 60, solubility: 18.0 },
    { temperature: 70, solubility: 19.7 },
    { temperature: 80, solubility: 21.4 },
    { temperature: 90, solubility: 23.1 },
    { temperature: 100, solubility: 24.1 }
  ],
  
  // Magnesium Sulfate [MgSO4]
  'mg_so4': [
    { temperature: 0, solubility: 22.0 },
    { temperature: 10, solubility: 25.5 },
    { temperature: 20, solubility: 33.7 },
    { temperature: 30, solubility: 40.8 },
    { temperature: 40, solubility: 48.1 },
    { temperature: 50, solubility: 54.4 },
    { temperature: 60, solubility: 61.3 },
    { temperature: 70, solubility: 68.3 },
    { temperature: 80, solubility: 72.1 },
    { temperature: 90, solubility: 73.8 },
    { temperature: 100, solubility: 74.0 }
  ],
  
  // Monopotassium Phosphate [KH2PO4]
  'kh2_po4': [
    { temperature: 0, solubility: 12.0 },
    { temperature: 10, solubility: 14.8 },
    { temperature: 20, solubility: 22.6 },
    { temperature: 30, solubility: 31.7 },
    { temperature: 40, solubility: 37.4 },
    { temperature: 50, solubility: 42.7 },
    { temperature: 60, solubility: 48.0 },
    { temperature: 70, solubility: 53.2 },
    { temperature: 80, solubility: 58.5 },
    { temperature: 90, solubility: 64.0 },
    { temperature: 100, solubility: 68.9 }
  ]
};

/**
 * Calculate solubility at a specific temperature using linear interpolation
 * @param saltId Identifier for the salt
 * @param temperature Temperature in Celsius
 * @returns Solubility in g/100mL or null if salt data not available
 */
export function calculateSolubilityAtTemperature(
  saltId: keyof typeof solubilityData,
  temperature: number
): number | null {
  const data = solubilityData[saltId];
  if (!data) return null;
  
  // Handle temperature at or beyond data boundaries
  if (temperature <= data[0].temperature) return data[0].solubility;
  if (temperature >= data[data.length - 1].temperature) return data[data.length - 1].solubility;
  
  // Find data points for interpolation
  let lowerIndex = 0;
  for (let i = 0; i < data.length; i++) {
    if (data[i].temperature <= temperature && data[i + 1].temperature >= temperature) {
      lowerIndex = i;
      break;
    }
  }
  
  const lowerPoint = data[lowerIndex];
  const upperPoint = data[lowerIndex + 1];
  
  // Linear interpolation
  const slope = (upperPoint.solubility - lowerPoint.solubility) / 
                (upperPoint.temperature - lowerPoint.temperature);
  
  return lowerPoint.solubility + slope * (temperature - lowerPoint.temperature);
}

/**
 * Check if a salt concentration is within solubility limits at given temperature
 * @param saltId Identifier for the salt
 * @param concentration Concentration in g/100mL
 * @param temperature Temperature in Celsius
 * @returns True if concentration is within solubility limits, false otherwise
 */
export function isSoluble(
  saltId: keyof typeof solubilityData,
  concentration: number,
  temperature: number
): boolean {
  const solubility = calculateSolubilityAtTemperature(saltId, temperature);
  if (solubility === null) return false; // Unknown salt
  
  return concentration <= solubility;
}

/**
 * Get the maximum safe concentration (with safety margin) for a salt at a given temperature
 * @param saltId Identifier for the salt
 * @param temperature Temperature in Celsius
 * @param safetyFactor Safety factor (percentage as decimal) to apply (default: 0.9 or 90%)
 * @returns Maximum safe concentration in g/100mL or null if salt data not available
 */
export function getMaxSafeConcentration(
  saltId: keyof typeof solubilityData,
  temperature: number,
  safetyFactor: number = 0.9
): number | null {
  const solubility = calculateSolubilityAtTemperature(saltId, temperature);
  if (solubility === null) return null;
  
  return solubility * safetyFactor;
}

/**
 * Find the minimum temperature required to dissolve a specific concentration of salt
 * @param saltId Identifier for the salt
 * @param concentration Concentration in g/100mL
 * @returns Minimum temperature in Celsius or null if impossible to dissolve or salt data not available
 */
export function findMinimumTemperatureForSolubility(
  saltId: keyof typeof solubilityData,
  concentration: number
): number | null {
  const data = solubilityData[saltId];
  if (!data) return null;
  
  // Check if concentration exceeds maximum possible solubility at highest temperature
  if (concentration > data[data.length - 1].solubility) {
    return null; // Impossible to dissolve at any temperature in our range
  }
  
  // Check if can dissolve at lowest temperature
  if (concentration <= data[0].solubility) {
    return data[0].temperature;
  }
  
  // Find temperature range
  let lowerIndex = 0;
  for (let i = 0; i < data.length - 1; i++) {
    if (data[i].solubility <= concentration && data[i + 1].solubility >= concentration) {
      lowerIndex = i;
      break;
    }
  }
  
  // Interpolate to find exact temperature
  const lowerPoint = data[lowerIndex];
  const upperPoint = data[lowerIndex + 1];
  
  const slope = (upperPoint.temperature - lowerPoint.temperature) / 
                (upperPoint.solubility - lowerPoint.solubility);
  
  return lowerPoint.temperature + slope * (concentration - lowerPoint.solubility);
}
