{"name": "flahafast", "version": "1.0.0", "private": true, "scripts": {"start": "node server/dist/index.js", "client:dev": "cd client && npm run dev", "server:dev": "cd server && npm run dev", "dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "build": "npm run build:client && npm run build:server", "build:client": "cd client && npm run build", "build:server": "cd server && npm run build", "postinstall": "cd client && npm install && cd ../server && npm install"}, "devDependencies": {"concurrently": "^8.2.2"}}