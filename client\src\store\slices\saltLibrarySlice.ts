import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface Salt {
  id: number;
  name: string;
  formula: string;
  elements: { [key: string]: number };
  price: number;
  purity: number;
}

interface SaltLibraryState {
  salts: Salt[];
  loading: boolean;
  error: string | null;
  selectedSalt: number | null;
}

const initialState: SaltLibraryState = {
  salts: [],
  loading: false,
  error: null,
  selectedSalt: null,
};

const saltLibrarySlice = createSlice({
  name: 'saltLibrary',
  initialState,
  reducers: {
    fetchSaltsStart(state) {
      state.loading = true;
      state.error = null;
    },
    fetchSaltsSuccess(state, action: PayloadAction<Salt[]>) {
      state.salts = action.payload;
      state.loading = false;
      state.error = null;
    },
    fetchSaltsFailed(state, action: PayloadAction<string>) {
      state.loading = false;
      state.error = action.payload;
    },
    addSalt(state, action: PayloadAction<Salt>) {
      state.salts.push(action.payload);
    },
    updateSalt(state, action: PayloadAction<Salt>) {
      const index = state.salts.findIndex(salt => salt.id === action.payload.id);
      if (index !== -1) {
        state.salts[index] = action.payload;
      }
    },
    deleteSalt(state, action: PayloadAction<number>) {
      state.salts = state.salts.filter(salt => salt.id !== action.payload);
    },
    setSelectedSalt(state, action: PayloadAction<number>) {
      state.selectedSalt = action.payload;
    },
  },
});

export const {
  fetchSaltsStart,
  fetchSaltsSuccess,
  fetchSaltsFailed,
  addSalt,
  updateSalt,
  deleteSalt,
  setSelectedSalt,
} = saltLibrarySlice.actions;

export default saltLibrarySlice.reducer;
