import { Request, Response } from 'express';
import { Formulation } from '../models/formulation';

// Get all formulations
export const getFormulations = async (req: Request, res: Response) => {
    try {
        const formulations = await Formulation.find();
        res.status(200).json(formulations);
    } catch (error) {
        res.status(500).json({ message: 'Error fetching formulations', error });
    }
};

// Create a new formulation
export const createFormulation = async (req: Request, res: Response) => {
    const newFormulation = new Formulation(req.body);
    try {
        const savedFormulation = await newFormulation.save();
        res.status(201).json(savedFormulation);
    } catch (error) {
        res.status(400).json({ message: 'Error creating formulation', error });
    }
};

// Update a formulation
export const updateFormulation = async (req: Request, res: Response) => {
    try {
        const updatedFormulation = await Formulation.findByIdAndUpdate(req.params.id, req.body, { new: true });
        if (!updatedFormulation) {
            return res.status(404).json({ message: 'Formulation not found' });
        }
        res.status(200).json(updatedFormulation);
    } catch (error) {
        res.status(400).json({ message: 'Error updating formulation', error });
    }
};

// Delete a formulation
export const deleteFormulation = async (req: Request, res: Response) => {
    try {
        const deletedFormulation = await Formulation.findByIdAndDelete(req.params.id);
        if (!deletedFormulation) {
            return res.status(404).json({ message: 'Formulation not found' });
        }
        res.status(200).json({ message: 'Formulation deleted successfully' });
    } catch (error) {
        res.status(500).json({ message: 'Error deleting formulation', error });
    }
};