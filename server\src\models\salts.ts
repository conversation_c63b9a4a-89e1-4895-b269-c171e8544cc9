export interface Salt {
    id: number;
    name: string;
    composition: string;
    molarMass: number;
    solubility: number;
}

export const salts: Salt[] = [
    {
        id: 1,
        name: "Calcium Nitrate",
        composition: "Ca(NO3)2",
        molarMass: 164.10,
        solubility: 121.0
    },
    {
        id: 2,
        name: "Potassium Nitrate",
        composition: "KNO3",
        molarMass: 101.10,
        solubility: 31.6
    },
    {
        id: 3,
        name: "Magnesium Sulfate",
        composition: "MgSO4",
        molarMass: 120.37,
        solubility: 35.0
    },
    {
        id: 4,
        name: "Ammonium Phosphate",
        composition: "(NH4)3PO4",
        molarMass: 149.09,
        solubility: 0.2
    }
];