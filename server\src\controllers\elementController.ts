import { Request, Response } from 'express';
import { prisma } from '../app';

export const ElementController = {
  // Get all elements
  getAllElements: async (req: Request, res: Response) => {
    try {
      const { includeRelations } = req.query;
      let elements;
      
      if (includeRelations === 'true') {
        elements = await prisma.element.findMany({
          include: {
            saltElements: {
              include: {
                salt: true
              }
            },
            targetElements: {
              include: {
                formulation: true
              }
            }
          }
        });
      } else {
        elements = await prisma.element.findMany();
      }
      
      return res.status(200).json(elements);
    } catch (error) {
      console.error(`Error fetching elements: ${error}`);
      return res.status(500).json({ error: 'Failed to fetch elements' });
    }
  },

  // Get element by ID
  getElementById: async (req: Request, res: Response) => {
    const { id } = req.params;
    const { includeRelations } = req.query;
    
    try {
      let element;
      
      if (includeRelations === 'true') {
        element = await prisma.element.findUnique({
          where: { id },
          include: {
            saltElements: {
              include: {
                salt: true
              }
            },
            targetElements: {
              include: {
                formulation: true
              }
            }
          }
        });
      } else {
        element = await prisma.element.findUnique({
          where: { id }
        });
      }
      
      if (!element) {
        return res.status(404).json({ error: 'Element not found' });
      }
      
      return res.status(200).json(element);
    } catch (error) {
      console.error(`Error fetching element: ${error}`);
      return res.status(500).json({ error: 'Failed to fetch element' });
    }
  },

  // Get element by symbol
  getElementBySymbol: async (req: Request, res: Response) => {
    const { symbol } = req.params;
    const { includeRelations } = req.query;
    
    try {
      let element;
      
      if (includeRelations === 'true') {
        element = await prisma.element.findUnique({
          where: { symbol },
          include: {
            saltElements: {
              include: {
                salt: true
              }
            },
            targetElements: {
              include: {
                formulation: true
              }
            }
          }
        });
      } else {
        element = await prisma.element.findUnique({
          where: { symbol }
        });
      }
      
      if (!element) {
        return res.status(404).json({ error: 'Element not found' });
      }
      
      return res.status(200).json(element);
    } catch (error) {
      console.error(`Error fetching element by symbol: ${error}`);
      return res.status(500).json({ error: 'Failed to fetch element' });
    }
  },

  // Create new element
  createElement: async (req: Request, res: Response) => {
    const { symbol, name, atomicWeight } = req.body;
    
    if (!symbol || !name || atomicWeight === undefined) {
      return res.status(400).json({ error: 'Symbol, name, and atomicWeight are required' });
    }
    
    try {
      // Check if element with the same symbol already exists
      const existingElement = await prisma.element.findUnique({
        where: { symbol }
      });
      
      if (existingElement) {
        return res.status(409).json({ error: `Element with symbol '${symbol}' already exists` });
      }
      
      const parsedAtomicWeight = parseFloat(atomicWeight);
      if (isNaN(parsedAtomicWeight)) {
        return res.status(400).json({ error: 'Atomic weight must be a valid number' });
      }
      
      const element = await prisma.element.create({
        data: {
          symbol,
          name,
          atomicWeight: parsedAtomicWeight
        }
      });
      
      return res.status(201).json(element);
    } catch (error) {
      console.error(`Error creating element: ${error}`);
      return res.status(500).json({ error: 'Failed to create element' });
    }
  },

  // Update element
  updateElement: async (req: Request, res: Response) => {
    const { id } = req.params;
    const { symbol, name, atomicWeight } = req.body;
    
    try {
      // Check if element with the new symbol already exists (if symbol is being updated)
      if (symbol) {
        const existingElement = await prisma.element.findUnique({
          where: { symbol }
        });
        
        if (existingElement && existingElement.id !== id) {
          return res.status(409).json({ error: `Element with symbol '${symbol}' already exists` });
        }
      }
      
      // Validate atomic weight if provided
      let parsedAtomicWeight;
      if (atomicWeight !== undefined) {
        parsedAtomicWeight = parseFloat(atomicWeight);
        if (isNaN(parsedAtomicWeight)) {
          return res.status(400).json({ error: 'Atomic weight must be a valid number' });
        }
      }
      
      const element = await prisma.element.update({
        where: { id },
        data: {
          ...(symbol && { symbol }),
          ...(name && { name }),
          ...(atomicWeight !== undefined && { atomicWeight: parsedAtomicWeight })
        }
      });
      
      return res.status(200).json(element);
    } catch (error) {
      console.error(`Error updating element: ${error}`);
      return res.status(500).json({ error: 'Failed to update element' });
    }
  },

  // Delete element
  deleteElement: async (req: Request, res: Response) => {
    const { id } = req.params;
    
    try {
      // Check if the element is used in salts
      const saltElementCount = await prisma.saltElement.count({
        where: { elementId: id }
      });
      
      if (saltElementCount > 0) {
        return res.status(409).json({ 
          error: 'Cannot delete element as it is used in one or more salts',
          count: saltElementCount
        });
      }
      
      // Check if the element is used in formulations
      const targetElementCount = await prisma.targetElement.count({
        where: { elementId: id }
      });
      
      if (targetElementCount > 0) {
        return res.status(409).json({ 
          error: 'Cannot delete element as it is used in one or more formulations',
          count: targetElementCount
        });
      }
      
      await prisma.element.delete({
        where: { id }
      });
      
      return res.status(204).send();
    } catch (error) {
      console.error(`Error deleting element: ${error}`);
      return res.status(500).json({ error: 'Failed to delete element' });
    }
  },
  
  // Search elements
  searchElements: async (req: Request, res: Response) => {
    const { term } = req.query;
    
    if (!term || typeof term !== 'string') {
      return res.status(400).json({ error: 'Search term is required' });
    }
    
    try {
      const elements = await prisma.element.findMany({
        where: {
          OR: [
            { symbol: { contains: term } },
            { name: { contains: term } }
          ]
        }
      });
      
      return res.status(200).json(elements);
    } catch (error) {
      console.error(`Error searching elements: ${error}`);
      return res.status(500).json({ error: 'Failed to search elements' });
    }
  }
};
