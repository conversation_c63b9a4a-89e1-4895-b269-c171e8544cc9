import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface WaterQualityElement {
  id: number;
  name: string;
  symbol: string;
  value: number;
  unit: string;
}

export interface WaterQualityData {
  name: string;
  ph: number;
  ec: number;
  tds: number;
  temperature: number;
  ca: number;
  mg: number;
  k: number;
  na: number;
  cl: number;
  so4: number;
  no3: number;
  hco3: number;
  co3: number;
}

interface WaterQualityState {
  elements: WaterQualityElement[];
  data: WaterQualityData | null;
  profiles: WaterQualityData[];
  loading: boolean;
  error: string | null;
  selectedWaterProfile: number | null;
}

const initialState: WaterQualityState = {
  elements: [],
  data: null,
  profiles: [],
  loading: false,
  error: null,
  selectedWaterProfile: null,
};

const waterQualitySlice = createSlice({
  name: 'waterQuality',
  initialState,
  reducers: {
    fetchWaterQualityStart(state) {
      state.loading = true;
      state.error = null;
    },
    fetchWaterQualitySuccess(state, action: PayloadAction<WaterQualityElement[]>) {
      state.elements = action.payload;
      state.loading = false;
      state.error = null;
    },
    fetchWaterQualityFailed(state, action: PayloadAction<string>) {
      state.loading = false;
      state.error = action.payload;
    },
    updateWaterQualityElement(state, action: PayloadAction<WaterQualityElement>) {
      const index = state.elements.findIndex(element => element.id === action.payload.id);
      if (index !== -1) {
        state.elements[index] = action.payload;
      }
    },    updateWaterQualityData(state, action: PayloadAction<WaterQualityData>) {
      state.data = action.payload;
      
      // Check if this profile already exists by name, update it if it does,
      // otherwise add it to the profiles array
      const profileIndex = state.profiles.findIndex(p => p.name === action.payload.name);
      if (profileIndex >= 0) {
        state.profiles[profileIndex] = action.payload;
      } else if (action.payload.name) {
        // Only add to profiles if it has a name
        state.profiles.push(action.payload);
      }
    },
    addWaterProfile(state, action: PayloadAction<WaterQualityData>) {
      // Check for duplicates before adding
      const exists = state.profiles.some(profile => profile.name === action.payload.name);
      if (!exists) {
        state.profiles.push(action.payload);
      }
    },
    updateWaterProfile(state, action: PayloadAction<{ index: number, data: WaterQualityData }>) {
      const { index, data } = action.payload;
      if (index >= 0 && index < state.profiles.length) {
        state.profiles[index] = data;
      }
    },    setSelectedWaterProfile(state, action: PayloadAction<number>) {
      state.selectedWaterProfile = action.payload;
      if (action.payload !== null && action.payload >= 0 && action.payload < state.profiles.length) {
        state.data = state.profiles[action.payload];
      }
    },
    deleteWaterProfile(state, action: PayloadAction<{ name: string } | { index: number }>) {
      if ('name' in action.payload) {
        // Delete by name
        state.profiles = state.profiles.filter(profile => profile.name !== action.payload.name);
      } else if ('index' in action.payload) {
        // Delete by index
        const index = action.payload.index;
        if (index >= 0 && index < state.profiles.length) {
          state.profiles.splice(index, 1);
        }
      }
    },
  },
});

export const {
  fetchWaterQualityStart,
  fetchWaterQualitySuccess,
  fetchWaterQualityFailed,
  updateWaterQualityElement,
  updateWaterQualityData,
  addWaterProfile,
  updateWaterProfile,
  setSelectedWaterProfile,
  deleteWaterProfile,
} = waterQualitySlice.actions;

export default waterQualitySlice.reducer;
