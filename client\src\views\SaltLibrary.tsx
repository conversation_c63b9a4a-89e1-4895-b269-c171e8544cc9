import React, { useState, useEffect } from 'react';
import { Box, Button, Container, Grid, Paper, Tab, Tabs, Typography, Alert, Snackbar, CircularProgress } from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import SaltsTable from '../components/SaltsTable';
import SaltCompositionCalculator from '../components/salt-library/SaltCompositionCalculator';
import SaltCompositionAnalysisV2 from '../components/salt-library/SaltCompositionAnalysisV2';
import { calculateMolecularWeight } from '../utils/elementCalculations';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`salt-library-tabpanel-${index}`}
      aria-labelledby={`salt-library-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const SaltLibrary: React.FC = () => {
  const [tabValue, setTabValue] = useState<number>(0);
  const [selectedSalt, setSelectedSalt] = useState<any | null>(null);
  const [saltMolecularWeight, setSaltMolecularWeight] = useState<number | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  const showNotification = (message: string) => {
    setSnackbarMessage(message);
    setSnackbarOpen(true);
  };

  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };
  // Calculate molecular weight when salt is selected
  useEffect(() => {
    if (selectedSalt && selectedSalt.elements && selectedSalt.elements.length > 0) {
      try {
        setLoading(true);
        setError(null);
        
        // Format elements into the format expected by calculateMolecularWeight function
        const formattedElements = selectedSalt.elements.map((elem: any) => ({
          symbol: elem.elementSymbol || elem.element?.symbol || elem.symbol,
          percentage: elem.percentage,
        }));
        
        const mw = calculateMolecularWeight(formattedElements);
        setSaltMolecularWeight(mw);
        
        if (!mw) {
          setError("Couldn't calculate molecular weight. Some elements may have invalid data.");
          showNotification("Couldn't calculate the molecular weight for this salt.");
        }
      } catch (error) {
        console.error("Error calculating molecular weight:", error);
        setError("Error calculating molecular weight");
        showNotification("Error calculating salt properties. Please try again.");
      } finally {
        setLoading(false);
      }
    } else {
      setSaltMolecularWeight(null);
    }
  }, [selectedSalt]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };
  const handleSaltSelect = (salt: any) => {
    setSelectedSalt(salt);
    // Switch to analysis tab when salt is selected
    setTabValue(1);
    setLoading(true);
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Salt Library
        </Typography>
        <Typography variant="body1" paragraph>
          Browse and manage hydroponics fertilizer salts and their compositions. Create custom salt 
          mixtures, analyze element distributions, and manage your salt library for nutrient solutions.
        </Typography>

        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
          <Tabs 
            value={tabValue} 
            onChange={handleTabChange}
            aria-label="salt library tabs"
          >
            <Tab label="Browse Salts" />
            {selectedSalt && <Tab label={`Analyze: ${selectedSalt.name}`} />}
            <Tab label="Create New Salt" />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <SaltsTable onSaltSelect={handleSaltSelect} />
        </TabPanel>        <TabPanel value={tabValue} index={1}>
          {selectedSalt && (
            <>
              {loading ? (
                <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
                  <CircularProgress />
                </Box>
              ) : error ? (
                <Alert severity="error" sx={{ mt: 2 }}>
                  {error}
                </Alert>
              ) : (
                <SaltCompositionAnalysisV2 
                  saltName={selectedSalt.name}
                  formula={selectedSalt.formula}
                  elements={selectedSalt.elements.map((elem: any) => ({
                    symbol: elem.elementSymbol || elem.element?.symbol || elem.symbol,
                    percentage: elem.percentage,
                    name: elem.element?.name || ''
                  }))}
                  density={selectedSalt.density}
                  solubility={selectedSalt.solubility}
                  molecularWeight={saltMolecularWeight || undefined}
                />
              )}
            </>
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <SaltCompositionCalculator />
        </TabPanel>      </Paper>
      
      {/* Notification Snackbar */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={4000}
        onClose={handleSnackbarClose}
        message={snackbarMessage}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      />
    </Container>
  );
};

export default SaltLibrary;
