import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import {
  Container,
  Grid,
  Box,
  Typography,
  Tab,
  Tabs,
  Paper
} from '@mui/material';
import { WaterQualityInputUpdated, WaterQualityDisplayUpdated, WaterQualityComparison } from '../components/water-quality';
import { RootState } from '../store';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`water-quality-tabpanel-${index}`}
      aria-labelledby={`water-quality-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `water-quality-tab-${index}`,
    'aria-controls': `water-quality-tabpanel-${index}`,
  };
}

const WaterQualityPage: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const waterData = useSelector((state: RootState) => state.waterQuality.data);
  const waterProfiles = useSelector((state: RootState) => state.waterQuality.profiles);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" gutterBottom>Water Quality Management</Typography>
      <Typography variant="body1" paragraph>
        Enter your source water parameters to ensure precise nutrient calculations.
        Accurate water quality data is essential for developing balanced nutrient solutions.
      </Typography>

      <Paper sx={{ borderRadius: 1, overflow: 'hidden' }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          aria-label="Water quality management tabs"
          variant="fullWidth"
        >          <Tab label="Enter Water Parameters" {...a11yProps(0)} />
          <Tab label="View Water Profile" {...a11yProps(1)} />
          <Tab label="Water Profiles Library" {...a11yProps(2)} />
          <Tab label="Compare Profiles" {...a11yProps(3)} />
        </Tabs>
      </Paper>      <TabPanel value={tabValue} index={0}>
        <WaterQualityInputUpdated onSave={() => setTabValue(1)} />
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        {waterData ? (
          <WaterQualityDisplayUpdated waterData={waterData} />
        ) : (
          <Box sx={{ py: 8, textAlign: 'center' }}>
            <Typography variant="h6">
              No water quality data available. Please enter water parameters in the 'Enter Water Parameters' tab.
            </Typography>
          </Box>
        )}
      </TabPanel>      <TabPanel value={tabValue} index={2}>
        <Box sx={{ mb: 4 }}>
          <Typography variant="h6" gutterBottom>Saved Water Profiles</Typography>          {waterProfiles && waterProfiles.length > 0 ? (
            <Box sx={{ display: 'grid', gap: 3 }}>
              {waterProfiles.map((profile: any, index: number) => (
                <Box key={index}>
                  <WaterQualityDisplayUpdated waterData={profile} />
                </Box>
              ))}
            </Box>
          ) : (
            <Box sx={{ py: 8, textAlign: 'center' }}>
              <Typography variant="body1">
                No water profiles saved yet. Create a profile in the 'Enter Water Parameters' tab.
              </Typography>
            </Box>
          )}
        </Box>
      </TabPanel>

      <TabPanel value={tabValue} index={3}>
        <WaterQualityComparison currentWaterData={waterData} />
      </TabPanel>
    </Container>
  );
};

export default WaterQualityPage;
