import { configureStore } from '@reduxjs/toolkit';
import { combineReducers } from 'redux';
import waterQualityReducer from './slices/waterQualitySlice';
import saltLibraryReducer from './slices/saltLibrarySlice';
import formulationReducer from './slices/formulationSlice';
import calculationReducer from './slices/calculationSlice';
import resultsReducer from './slices/resultsSlice';

const rootReducer = combineReducers({
  waterQuality: waterQualityReducer,
  saltLibrary: saltLibraryReducer,
  formulation: formulationReducer,
  calculation: calculationReducer,
  results: resultsReducer,
});

const store = configureStore({
  reducer: rootReducer,
});

export type RootState = ReturnType<typeof rootReducer>;
export type AppDispatch = typeof store.dispatch;

export default store;