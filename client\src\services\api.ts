import axios from 'axios';

const API_BASE_URL = 'http://localhost:5000/api'; // Update with your backend URL

// Function to get water quality data
export const getWaterQuality = async () => {
    try {
        const response = await axios.get(`${API_BASE_URL}/water-quality`);
        return response.data;
    } catch (error) {
        throw new Error('Error fetching water quality data');
    }
};

// Function to get salts data
export const getSalts = async () => {
    try {
        const response = await axios.get(`${API_BASE_URL}/salts`);
        return response.data;
    } catch (error) {
        throw new Error('Error fetching salts data');
    }
};

// Function to calculate formulation
export const calculateFormulation = async (data) => {
    try {
        const response = await axios.post(`${API_BASE_URL}/calculate/formulation`, data);
        return response.data;
    } catch (error) {
        throw new Error('Error calculating formulation');
    }
};

// Function to get formulation data
export const getFormulations = async () => {
    try {
        const response = await axios.get(`${API_BASE_URL}/formulations`);
        return response.data;
    } catch (error) {
        throw new Error('Error fetching formulations data');
    }
};