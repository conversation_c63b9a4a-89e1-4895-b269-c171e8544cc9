import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  TextField,
  Button,
  Card,
  CardContent,
  CardHeader,
  Grid,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Typography,
  Paper,
  Box,
  Snackbar,
  Alert,
  CircularProgress,
  FormHelperText,
} from '@mui/material';
import { updateWaterQualityData } from '../../store/slices/waterQualitySlice';
import { RootState } from '../../store/store';

interface WaterQualityInputProps {
  onSave?: (data: any) => void;
}

const WaterQualityInputUpdated: React.FC<WaterQualityInputProps> = ({ onSave }) => {
  // Default water quality parameters
  const initialState = {
    name: '',
    ph: 7.0,
    ec: 0.0,
    tds: 0,
    temperature: 20, // in Celsius
    ca: 0.0,
    mg: 0.0,
    k: 0.0,
    na: 0.0,
    cl: 0.0,
    so4: 0.0,
    no3: 0.0,
    hco3: 0.0,
    co3: 0.0
  };

  const dispatch = useDispatch();
  const existingData = useSelector((state: RootState) => state.waterQuality.data);
  
  const [waterData, setWaterData] = useState(existingData || initialState);
  const [ecUnit, setEcUnit] = useState<'mS/cm' | 'μS/cm'>('mS/cm');
  const [tdsUnit, setTdsUnit] = useState<'ppm500' | 'ppm640' | 'ppm700'>('ppm500');
  const [temperatureUnit, setTemperatureUnit] = useState<'C' | 'F'>('C');
  const [ionUnit, setIonUnit] = useState<'ppm' | 'meq' | 'mM'>('ppm');
  const [calculated, setCalculated] = useState({
    dGH: 0,
    dKH: 0
  });
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [loading, setLoading] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error' | 'warning' | 'info'>('success');

  // Convert EC when unit changes
  useEffect(() => {
    convertEC();
  }, [ecUnit]);

  // Convert TDS when unit changes
  useEffect(() => {
    convertTDS();
  }, [tdsUnit]);

  // Convert Temperature when unit changes
  useEffect(() => {
    convertTemperature();
  }, [temperatureUnit]);

  // Calculate water hardness
  useEffect(() => {
    calculateHardness();
  }, [waterData.ca, waterData.mg, waterData.hco3, waterData.co3, ionUnit]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    
    // Convert string input to number where needed
    const isNumericField = ['ph', 'ec', 'tds', 'temperature', 'ca', 'mg', 'k', 'na', 'cl', 'so4', 'no3', 'hco3', 'co3'].includes(name);
    const numValue = isNumericField ? parseFloat(value) : null;
    const newValue = isNumericField ? (value === '' ? 0 : numValue) : value;
    
    // Clear any previous error for this field
    setErrors(prev => ({ ...prev, [name]: undefined }));
    
    // Validate inputs
    if (isNumericField && value !== '') {
      if (isNaN(numValue as number)) {
        setErrors(prev => ({ ...prev, [name]: 'Must be a number' }));
      } else if (name === 'ph' && (numValue! < 0 || numValue! > 14)) {
        setErrors(prev => ({ ...prev, [name]: 'pH must be between 0 and 14' }));
      } else if (name === 'ec' && numValue! < 0) {
        setErrors(prev => ({ ...prev, [name]: 'EC cannot be negative' }));
      } else if (name === 'tds' && numValue! < 0) {
        setErrors(prev => ({ ...prev, [name]: 'TDS cannot be negative' }));
      } else if (['ca', 'mg', 'k', 'na', 'cl', 'so4', 'no3', 'hco3', 'co3'].includes(name) && numValue! < 0) {
        setErrors(prev => ({ ...prev, [name]: 'Concentration cannot be negative' }));
      }
    } else if (name === 'name' && value === '') {
      setErrors(prev => ({ ...prev, [name]: 'Name is required' }));
    }
      
    setWaterData(prev => ({ ...prev, [name]: newValue }));
  };

  const convertEC = () => {
    // Implement EC conversion between mS/cm and μS/cm
    if (ecUnit === 'mS/cm') {
      // Convert from μS/cm to mS/cm if needed
      if (waterData.ec > 100) {
        setWaterData(prev => ({ ...prev, ec: prev.ec / 1000 }));
      }
    } else {
      // Convert from mS/cm to μS/cm
      if (waterData.ec < 100) {
        setWaterData(prev => ({ ...prev, ec: prev.ec * 1000 }));
      }
    }
  };

  const convertTDS = () => {
    // Implement TDS conversion between different PPM scales
    const tdsFactor = {
      'ppm500': 1,
      'ppm640': 1.28,  // ppm640 = ppm500 * 1.28
      'ppm700': 1.4    // ppm700 = ppm500 * 1.4
    };
    
    const currentFactor = tdsFactor[tdsUnit];
    const baseFactor = tdsFactor['ppm500'];
    
    const baseValue = waterData.tds / currentFactor * baseFactor;
    
    setWaterData(prev => ({ ...prev, tds: Math.round(baseValue * currentFactor) }));
  };

  const convertTemperature = () => {
    // Implement temperature conversion between Celsius and Fahrenheit
    if (temperatureUnit === 'C' && waterData.temperature > 50) {
      // Likely in Fahrenheit, convert to Celsius
      setWaterData(prev => ({ 
        ...prev, 
        temperature: Math.round((prev.temperature - 32) * 5 / 9 * 10) / 10
      }));
    } else if (temperatureUnit === 'F' && waterData.temperature < 40) {
      // Likely in Celsius, convert to Fahrenheit
      setWaterData(prev => ({
        ...prev,
        temperature: Math.round((prev.temperature * 9 / 5 + 32) * 10) / 10
      }));
    }
  };

  const calculateHardness = () => {
    // Simplified hardness calculation
    // In a real implementation, this would use the unit conversion utilities
    let GH = 0;
    let KH = 0;
    
    if (ionUnit === 'ppm') {
      // Calculate German Hardness (dGH) from Ca and Mg in ppm
      GH = (waterData.ca / 7.14) + (waterData.mg / 4.33);
      
      // Calculate Carbonate Hardness (dKH) from HCO3 and CO3 in ppm
      KH = (waterData.hco3 / 21.8) + (waterData.co3 / 10.9);
    } else if (ionUnit === 'meq') {
      // Calculate from meq/L
      GH = (waterData.ca * 2.5) + (waterData.mg * 4.1);
      KH = waterData.hco3 + (waterData.co3 * 2);
    } else if (ionUnit === 'mM') {
      // Calculate from mmol/L
      GH = (waterData.ca * 5) + (waterData.mg * 8.2);
      KH = waterData.hco3 + (waterData.co3 * 2);
    }
    
    setCalculated({
      dGH: Math.round(GH * 10) / 10,
      dKH: Math.round(KH * 10) / 10
    });
  };

  // Validate all fields before saving
  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};
    
    // Check required fields
    if (!waterData.name) {
      newErrors.name = 'Name is required';
    }
    
    // Validate numeric ranges
    if (waterData.ph < 0 || waterData.ph > 14) {
      newErrors.ph = 'pH must be between 0 and 14';
    }
    
    if (waterData.ec < 0) {
      newErrors.ec = 'EC cannot be negative';
    }
    
    if (waterData.tds < 0) {
      newErrors.tds = 'TDS cannot be negative';
    }
    
    // Check all ion concentrations
    ['ca', 'mg', 'k', 'na', 'cl', 'so4', 'no3', 'hco3', 'co3'].forEach(ion => {
      if ((waterData as any)[ion] < 0) {
        newErrors[ion] = 'Concentration cannot be negative';
      }
    });
    
    // Update state with all validation errors
    setErrors(newErrors);
    
    // Return true if no errors
    return Object.keys(newErrors).length === 0;
  };

  const handleSaveClick = async () => {
    // Validate all fields
    if (!validateForm()) {
      setSnackbarMessage('Please fix the input errors before saving');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
      return;
    }
    
    // Show loading indicator
    setLoading(true);
    
    try {
      // In a real app, you might have API call to save the data
      // For now, we'll just wait a moment to simulate the API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Save water quality data to store
      dispatch(updateWaterQualityData(waterData));
      
      // Notify parent component
      if (onSave) onSave(waterData);
      
      // Display success message
      setSnackbarMessage('Water profile saved successfully');
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
    } catch (error) {
      console.error('Error saving water profile:', error);
      setSnackbarMessage('Failed to save water profile');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    } finally {
      setLoading(false);
    }
  };

  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  return (
    <Card sx={{ mb: 4 }}>
      <CardHeader 
        title="Water Quality Parameters" 
        subheader="Enter your water source quality parameters"
      />
      <CardContent>
        <Grid container spacing={3}>
          <Grid xs={12} md={6}>
            <TextField
              fullWidth
              label="Water Profile Name"
              name="name"
              value={waterData.name}
              onChange={handleChange}
              margin="normal"
              variant="outlined"
              error={!!errors.name}
              helperText={errors.name}
              required
            />
          </Grid>

          <Grid xs={12} md={6}>
            <TextField
              fullWidth
              label="pH"
              name="ph"
              type="number"
              value={waterData.ph}
              onChange={handleChange}
              margin="normal"
              variant="outlined"
              inputProps={{ step: "0.1", min: "0", max: "14" }}
              error={!!errors.ph}
              helperText={errors.ph}
            />
          </Grid>

          <Grid xs={12} md={6}>
            <TextField
              fullWidth
              label="EC"
              name="ec"
              type="number"
              value={waterData.ec}
              onChange={handleChange}
              margin="normal"
              variant="outlined"
              error={!!errors.ec}
              helperText={errors.ec}
              InputProps={{
                endAdornment: (
                  <FormControl variant="standard" sx={{ minWidth: 80 }}>
                    <Select
                      value={ecUnit}
                      onChange={(e) => setEcUnit(e.target.value as 'mS/cm' | 'μS/cm')}
                    >
                      <MenuItem value="mS/cm">mS/cm</MenuItem>
                      <MenuItem value="μS/cm">μS/cm</MenuItem>
                    </Select>
                  </FormControl>
                )
              }}
            />
          </Grid>

          <Grid xs={12} md={6}>
            <TextField
              fullWidth
              label="TDS"
              name="tds"
              type="number"
              value={waterData.tds}
              onChange={handleChange}
              margin="normal"
              variant="outlined"
              error={!!errors.tds}
              helperText={errors.tds}
              InputProps={{
                endAdornment: (
                  <FormControl variant="standard" sx={{ minWidth: 80 }}>
                    <Select
                      value={tdsUnit}
                      onChange={(e) => setTdsUnit(e.target.value as 'ppm500' | 'ppm640' | 'ppm700')}
                    >
                      <MenuItem value="ppm500">ppm (500)</MenuItem>
                      <MenuItem value="ppm640">ppm (640)</MenuItem>
                      <MenuItem value="ppm700">ppm (700)</MenuItem>
                    </Select>
                  </FormControl>
                )
              }}
            />
          </Grid>

          <Grid xs={12} md={6}>
            <TextField
              fullWidth
              label="Temperature"
              name="temperature"
              type="number"
              value={waterData.temperature}
              onChange={handleChange}
              margin="normal"
              variant="outlined"
              error={!!errors.temperature}
              helperText={errors.temperature}
              InputProps={{
                endAdornment: (
                  <FormControl variant="standard" sx={{ minWidth: 30 }}>
                    <Select
                      value={temperatureUnit}
                      onChange={(e) => setTemperatureUnit(e.target.value as 'C' | 'F')}
                    >
                      <MenuItem value="C">°C</MenuItem>
                      <MenuItem value="F">°F</MenuItem>
                    </Select>
                  </FormControl>
                )
              }}
            />
          </Grid>

          <Grid xs={12}>
            <Box sx={{ mb: 2 }}>
              <Typography variant="h6">Ion Concentrations</Typography>
              <FormControl variant="outlined" sx={{ minWidth: 100, ml: 2 }}>
                <InputLabel>Unit</InputLabel>
                <Select
                  value={ionUnit}
                  onChange={(e) => setIonUnit(e.target.value as 'ppm' | 'meq' | 'mM')}
                  label="Unit"
                >
                  <MenuItem value="ppm">ppm</MenuItem>
                  <MenuItem value="meq">meq/L</MenuItem>
                  <MenuItem value="mM">mM</MenuItem>
                </Select>
              </FormControl>
            </Box>
          </Grid>

          {/* Cations Section */}
          <Grid xs={12} md={6}>
            <Paper elevation={1} sx={{ p: 2 }}>
              <Typography variant="subtitle1" gutterBottom>Cations</Typography>
              <Grid container spacing={2}>
                <Grid xs={6}>
                  <TextField
                    fullWidth
                    label="Ca²⁺"
                    name="ca"
                    type="number"
                    value={waterData.ca}
                    onChange={handleChange}
                    variant="outlined"
                    size="small"
                    error={!!errors.ca}
                    helperText={errors.ca}
                  />
                </Grid>
                <Grid xs={6}>
                  <TextField
                    fullWidth
                    label="Mg²⁺"
                    name="mg"
                    type="number"
                    value={waterData.mg}
                    onChange={handleChange}
                    variant="outlined"
                    size="small"
                    error={!!errors.mg}
                    helperText={errors.mg}
                  />
                </Grid>
                <Grid xs={6}>
                  <TextField
                    fullWidth
                    label="K⁺"
                    name="k"
                    type="number"
                    value={waterData.k}
                    onChange={handleChange}
                    variant="outlined"
                    size="small"
                    error={!!errors.k}
                    helperText={errors.k}
                  />
                </Grid>
                <Grid xs={6}>
                  <TextField
                    fullWidth
                    label="Na⁺"
                    name="na"
                    type="number"
                    value={waterData.na}
                    onChange={handleChange}
                    variant="outlined"
                    size="small"
                    error={!!errors.na}
                    helperText={errors.na}
                  />
                </Grid>
              </Grid>
            </Paper>
          </Grid>

          {/* Anions Section */}
          <Grid xs={12} md={6}>
            <Paper elevation={1} sx={{ p: 2 }}>
              <Typography variant="subtitle1" gutterBottom>Anions</Typography>
              <Grid container spacing={2}>
                <Grid xs={6}>
                  <TextField
                    fullWidth
                    label="Cl⁻"
                    name="cl"
                    type="number"
                    value={waterData.cl}
                    onChange={handleChange}
                    variant="outlined"
                    size="small"
                    error={!!errors.cl}
                    helperText={errors.cl}
                  />
                </Grid>
                <Grid xs={6}>
                  <TextField
                    fullWidth
                    label="SO₄²⁻"
                    name="so4"
                    type="number"
                    value={waterData.so4}
                    onChange={handleChange}
                    variant="outlined"
                    size="small"
                    error={!!errors.so4}
                    helperText={errors.so4}
                  />
                </Grid>
                <Grid xs={6}>
                  <TextField
                    fullWidth
                    label="NO₃⁻"
                    name="no3"
                    type="number"
                    value={waterData.no3}
                    onChange={handleChange}
                    variant="outlined"
                    size="small"
                    error={!!errors.no3}
                    helperText={errors.no3}
                  />
                </Grid>
                <Grid xs={6}>
                  <TextField
                    fullWidth
                    label="HCO₃⁻"
                    name="hco3"
                    type="number"
                    value={waterData.hco3}
                    onChange={handleChange}
                    variant="outlined"
                    size="small"
                    error={!!errors.hco3}
                    helperText={errors.hco3}
                  />
                </Grid>
                <Grid xs={6}>
                  <TextField
                    fullWidth
                    label="CO₃²⁻"
                    name="co3"
                    type="number"
                    value={waterData.co3}
                    onChange={handleChange}
                    variant="outlined"
                    size="small"
                    error={!!errors.co3}
                    helperText={errors.co3}
                  />
                </Grid>
              </Grid>
            </Paper>
          </Grid>

          {/* Calculated Values Section */}
          <Grid xs={12}>
            <Paper elevation={1} sx={{ p: 2, mt: 2, bgcolor: '#f5f5f5' }}>
              <Typography variant="subtitle1" gutterBottom>Calculated Values</Typography>
              <Grid container spacing={2}>
                <Grid xs={6} md={3}>
                  <Typography variant="body2" color="text.secondary">German Hardness (dGH)</Typography>
                  <Typography variant="h6">{calculated.dGH} °dH</Typography>
                </Grid>
                <Grid xs={6} md={3}>
                  <Typography variant="body2" color="text.secondary">Carbonate Hardness (dKH)</Typography>
                  <Typography variant="h6">{calculated.dKH} °dH</Typography>
                </Grid>
              </Grid>
            </Paper>
          </Grid>

          <Grid xs={12}>
            <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
              <Button 
                variant="contained" 
                color="primary" 
                onClick={handleSaveClick}
                disabled={loading || Object.keys(errors).length > 0}
                startIcon={loading ? <CircularProgress size={20} color="inherit" /> : null}
              >
                {loading ? 'Saving...' : 'Save Water Profile'}
              </Button>
            </Box>
          </Grid>
        </Grid>
      </CardContent>
      
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert 
          onClose={handleSnackbarClose} 
          severity={snackbarSeverity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Card>
  );
};

export default WaterQualityInputUpdated;
