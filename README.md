# HydroBuddy PWA

HydroBuddy is a Progressive Web Application (PWA) designed to assist users in managing and calculating hydroponic nutrient solutions. This application provides a user-friendly interface for water quality analysis, salt management, formulation creation, and result visualization.

## Features

- **Water Quality Management**: Analyze and manage water quality parameters.
- **Salt Library**: Access and manage a comprehensive library of salts used in hydroponics.
- **Formulation Creation**: Create and save custom nutrient formulations.
- **Calculation Engine**: Perform complex calculations for nutrient solutions.
- **Results Visualization**: View and export results in various formats.

## Getting Started

### Prerequisites

- Node.js (version 14 or higher)
- npm (Node package manager)
- Docker (for containerized deployment)

### Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/hydrobuddy-pwa.git
   ```

2. Navigate to the client directory:
   ```
   cd hydrobuddy-pwa/client
   ```

3. Install dependencies:
   ```
   npm install
   ```

4. Navigate to the server directory:
   ```
   cd ../server
   ```

5. Install server dependencies:
   ```
   npm install
   ```

### Running the Application

To run the application in development mode, follow these steps:

1. Start the server:
   ```
   cd server
   npm run dev
   ```

2. In a new terminal, start the client:
   ```
   cd client
   npm run dev
   ```

3. Open your browser and navigate to `http://localhost:3000` to access the application.

### Docker Deployment

To run the application using Docker, execute the following command in the root directory of the project:

```
docker-compose up
```

This command will build and start the application services defined in the `docker-compose.yml` file.

## Contributing

Contributions are welcome! Please open an issue or submit a pull request for any enhancements or bug fixes.

## License

This project is licensed under the MIT License. See the LICENSE file for details.