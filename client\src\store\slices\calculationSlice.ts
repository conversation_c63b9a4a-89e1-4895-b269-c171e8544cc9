import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface CalculationState {
  volume: number;
  volumeUnit: string;
  precision: number;
  optimizationTarget: 'cost' | 'salts' | 'balance';
  isCalculating: boolean;
  error: string | null;
}

const initialState: CalculationState = {
  volume: 100,
  volumeUnit: 'L',
  precision: 2,
  optimizationTarget: 'balance',
  isCalculating: false,
  error: null,
};

const calculationSlice = createSlice({
  name: 'calculation',
  initialState,
  reducers: {
    setVolume(state, action: PayloadAction<number>) {
      state.volume = action.payload;
    },
    setVolumeUnit(state, action: PayloadAction<string>) {
      state.volumeUnit = action.payload;
    },
    setPrecision(state, action: PayloadAction<number>) {
      state.precision = action.payload;
    },
    setOptimizationTarget(state, action: PayloadAction<'cost' | 'salts' | 'balance'>) {
      state.optimizationTarget = action.payload;
    },
    startCalculation(state) {
      state.isCalculating = true;
      state.error = null;
    },
    calculationSuccess(state) {
      state.isCalculating = false;
    },
    calculationFailed(state, action: PayloadAction<string>) {
      state.isCalculating = false;
      state.error = action.payload;
    },
  },
});

export const {
  setVolume,
  setVolumeUnit,
  setPrecision,
  setOptimizationTarget,
  startCalculation,
  calculationSuccess,
  calculationFailed,
} = calculationSlice.actions;

export default calculationSlice.reducer;
