/**
 * Utility functions for exporting data from the application
 */

/**
 * Exports data as a downloadable JSON file
 * @param data - Any JavaScript object or array to export
 * @param filename - Name of the file (without extension)
 */
export const exportToJson = (data: any, filename: string): void => {
  // Format the data as pretty JSON with indentation
  const jsonString = JSON.stringify(data, null, 2);
  
  // Create a blob with the JSON content
  const blob = new Blob([jsonString], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  
  // Create a temporary download link
  const link = document.createElement('a');
  link.href = url;
  link.download = `${filename.replace(/\s+/g, '_')}.json`;
  
  // Trigger the download
  document.body.appendChild(link);
  link.click();
  
  // Clean up
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

/**
 * Exports data as a downloadable CSV file
 * @param headers - Array of column headers
 * @param rows - Array of arrays containing row data
 * @param filename - Name of the file (without extension)
 */
export const exportToCsv = (headers: string[], rows: (string | number)[][], filename: string): void => {
  // Format the CSV content
  const csvContent = [
    headers.join(','),
    ...rows.map(row => row.map(cell => {
      // Handle special characters and commas in the data
      const cellStr = String(cell);
      return cellStr.includes(',') || cellStr.includes('"') || cellStr.includes('\n')
        ? `"${cellStr.replace(/"/g, '""')}"`
        : cellStr;
    }).join(','))
  ].join('\n');
  
  // Create a blob with the CSV content
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const url = URL.createObjectURL(blob);
  
  // Create a temporary download link
  const link = document.createElement('a');
  link.href = url;
  link.download = `${filename.replace(/\s+/g, '_')}.csv`;
  
  // Trigger the download
  document.body.appendChild(link);
  link.click();
  
  // Clean up
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

/**
 * Format a date object as a string suitable for filenames
 * @param date - Date object to format
 * @returns Formatted date string (YYYY-MM-DD)
 */
export const formatDateForFilename = (date: Date = new Date()): string => {
  return date.toISOString().split('T')[0];
};

export default {
  exportToJson,
  exportToCsv,
  formatDateForFilename
};
