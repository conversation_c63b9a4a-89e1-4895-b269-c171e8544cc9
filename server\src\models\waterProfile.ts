import { Schema, model } from 'mongoose';

const waterProfileSchema = new Schema({
  name: {
    type: String,
    required: true,
  },
  pH: {
    type: Number,
    required: true,
  },
  electricalConductivity: {
    type: Number,
    required: true,
  },
  temperature: {
    type: Number,
    required: true,
  },
  nutrients: {
    type: Map,
    of: Number,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

const WaterProfile = model('WaterProfile', waterProfileSchema);

export default WaterProfile;