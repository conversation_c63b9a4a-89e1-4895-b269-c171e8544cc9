import { Router } from 'express';
import { ElementController } from '../controllers/elementController';
// Import controllers as they are implemented
import { SaltController } from '../controllers/saltController';
import { WaterProfileController } from '../controllers/waterProfileController';
import { FormulationController } from '../controllers/formulationController';

const router = Router();

// Element routes
router.get('/elements', ElementController.getAllElements);
router.get('/elements/search', ElementController.searchElements);
router.get('/elements/symbol/:symbol', ElementController.getElementBySymbol);
router.get('/elements/:id', ElementController.getElementById);
router.post('/elements', ElementController.createElement);
router.put('/elements/:id', ElementController.updateElement);
router.delete('/elements/:id', ElementController.deleteElement);

// Salt routes
router.get('/salts', SaltController.getAllSalts);
router.get('/salts/search', SaltController.searchSalts);
router.get('/salts/custom', SaltController.getCustomSalts);
router.get('/salts/:id', SaltController.getSaltById);
router.post('/salts', SaltController.createSalt);
router.put('/salts/:id', SaltController.updateSalt);
router.delete('/salts/:id', SaltController.deleteSalt);

// Water Profile routes
router.get('/water-profiles', WaterProfileController.getAllWaterProfiles);
router.get('/water-profiles/search', WaterProfileController.searchWaterProfiles);
router.get('/water-profiles/:id', WaterProfileController.getWaterProfileById);
router.post('/water-profiles', WaterProfileController.createWaterProfile);
router.put('/water-profiles/:id', WaterProfileController.updateWaterProfile);
router.delete('/water-profiles/:id', WaterProfileController.deleteWaterProfile);

// Formulation routes
router.get('/formulations', FormulationController.getAllFormulations);
router.get('/formulations/search', FormulationController.searchFormulations);
router.get('/formulations/:id', FormulationController.getFormulationById);
router.post('/formulations', FormulationController.createFormulation);
router.put('/formulations/:id', FormulationController.updateFormulation);
router.delete('/formulations/:id', FormulationController.deleteFormulation);

// Health check route
router.get('/health', (_req, res) => {
  res.json({ status: 'ok', message: 'FlahaFast API is running' });
});

export default router;
