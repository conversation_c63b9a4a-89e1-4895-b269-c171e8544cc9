# Dependencies
node_modules/
.pnp/
.pnp.js

# Build outputs
dist/
build/
*.tsbuildinfo
.next/
out/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env*.local

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Testing
coverage/
.nyc_output/

# IDE specific files
.idea/
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
*.code-workspace
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.project
.classpath
.settings/
*.sublime-project
*.sublime-workspace

# Prisma
prisma/dev.db
prisma/dev.db-journal

# TypeScript
*.tsbuildinfo

# Vite
.vite/
.rollup.cache/

# Temp files
.temp/
.tmp/

# Database
*.sqlite
*.sqlite3
*.db

# Pascal source backup files
*.bak

# Misc
.cache/
