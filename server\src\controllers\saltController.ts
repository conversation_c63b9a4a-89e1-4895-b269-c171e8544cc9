import { Request, Response } from 'express';
import { prisma } from '../app';

export const SaltController = {
  // Get all salts
  getAllSalts: async (req: Request, res: Response) => {
    try {
      const { includeElements } = req.query;
      let salts;
      
      if (includeElements === 'true') {
        salts = await prisma.salt.findMany({
          include: {
            elements: {
              include: {
                element: true
              }
            },
            formulations: {
              include: {
                formulation: true
              }
            }
          }
        });
      } else {
        salts = await prisma.salt.findMany();
      }
      
      return res.status(200).json(salts);
    } catch (error) {
      console.error(`Error fetching salts: ${error}`);
      return res.status(500).json({ error: 'Failed to fetch salts' });
    }
  },

  // Get salt by ID
  getSaltById: async (req: Request, res: Response) => {
    const { id } = req.params;
    const { includeElements } = req.query;
    
    try {
      let salt;
      
      if (includeElements === 'true') {
        salt = await prisma.salt.findUnique({
          where: { id },
          include: {
            elements: {
              include: {
                element: true
              }
            },
            formulations: {
              include: {
                formulation: true
              }
            }
          }
        });
      } else {
        salt = await prisma.salt.findUnique({
          where: { id }
        });
      }
      
      if (!salt) {
        return res.status(404).json({ error: 'Salt not found' });
      }
      
      return res.status(200).json(salt);
    } catch (error) {
      console.error(`Error fetching salt: ${error}`);
      return res.status(500).json({ error: 'Failed to fetch salt' });
    }
  },

  // Create new salt
  createSalt: async (req: Request, res: Response) => {
    const { name, formula, isCustom, density, solubility, elements } = req.body;
    
    if (!name) {
      return res.status(400).json({ error: 'Salt name is required' });
    }
    
    try {
      // Check if a salt with the same name already exists
      const existingSalt = await prisma.salt.findFirst({
        where: { name }
      });
      
      if (existingSalt) {
        return res.status(409).json({ error: `Salt with name '${name}' already exists` });
      }
      
      // Start a transaction to create the salt and its elements
      const result = await prisma.$transaction(async (prisma) => {
        // Create the salt
        const salt = await prisma.salt.create({
          data: {
            name,
            formula,
            isCustom: isCustom ?? false,
            density: density ? parseFloat(density) : null,
            solubility: solubility ? parseFloat(solubility) : null
          }
        });
        
        // Add elements if provided
        if (elements && Array.isArray(elements) && elements.length > 0) {
          for (const elem of elements) {
            const { elementId, symbol, percentage } = elem;
            
            let elementRecord;
            
            // Find element by ID or symbol
            if (elementId) {
              elementRecord = await prisma.element.findUnique({
                where: { id: elementId }
              });
            } else if (symbol) {
              elementRecord = await prisma.element.findUnique({
                where: { symbol }
              });
            }
            
            if (!elementRecord) {
              throw new Error(`Element with ID ${elementId} or symbol ${symbol} not found`);
            }
            
            if (!percentage || isNaN(parseFloat(percentage))) {
              throw new Error('Valid percentage is required for each element in a salt');
            }
            
            // Create the salt-element relation
            await prisma.saltElement.create({
              data: {
                saltId: salt.id,
                elementId: elementRecord.id,
                percentage: parseFloat(percentage)
              }
            });
          }
        }
        
        // Return the salt with its elements
        return prisma.salt.findUnique({
          where: { id: salt.id },
          include: {
            elements: {
              include: {
                element: true
              }
            }
          }
        });
      });
      
      return res.status(201).json(result);    } catch (error) {
      console.error(`Error creating salt: ${error}`);
      return res.status(500).json({ error: 'Failed to create salt' });
    }
  },

  // Update salt
  updateSalt: async (req: Request, res: Response) => {
    const { id } = req.params;
    const { name, formula, isCustom, density, solubility, elements } = req.body;
    
    try {
      // Check if salt exists
      const existingSalt = await prisma.salt.findUnique({
        where: { id }
      });
      
      if (!existingSalt) {
        return res.status(404).json({ error: 'Salt not found' });
      }
      
      // Check if new name conflicts with existing salt
      if (name && name !== existingSalt.name) {
        const nameConflict = await prisma.salt.findFirst({
          where: { name }
        });
        
        if (nameConflict) {
          return res.status(409).json({ error: `Salt with name '${name}' already exists` });
        }
      }
      
      // Update the salt in a transaction
      const result = await prisma.$transaction(async (prisma) => {
        // Update the salt properties
        const salt = await prisma.salt.update({
          where: { id },
          data: {
            ...(name && { name }),
            ...(formula !== undefined && { formula }),
            ...(isCustom !== undefined && { isCustom }),
            ...(density !== undefined && { density: density ? parseFloat(density) : null }),
            ...(solubility !== undefined && { solubility: solubility ? parseFloat(solubility) : null })
          }
        });
        
        // Update elements if provided
        if (elements && Array.isArray(elements)) {
          // First, remove all existing salt-element relations
          await prisma.saltElement.deleteMany({
            where: { saltId: id }
          });
          
          // Then create new relations
          for (const elem of elements) {
            const { elementId, symbol, percentage } = elem;
            
            let elementRecord;
            
            // Find element by ID or symbol
            if (elementId) {
              elementRecord = await prisma.element.findUnique({
                where: { id: elementId }
              });
            } else if (symbol) {
              elementRecord = await prisma.element.findUnique({
                where: { symbol }
              });
            }
            
            if (!elementRecord) {
              throw new Error(`Element with ID ${elementId} or symbol ${symbol} not found`);
            }
            
            if (!percentage || isNaN(parseFloat(percentage))) {
              throw new Error('Valid percentage is required for each element in a salt');
            }
            
            // Create the salt-element relation
            await prisma.saltElement.create({
              data: {
                saltId: salt.id,
                elementId: elementRecord.id,
                percentage: parseFloat(percentage)
              }
            });
          }
        }
        
        // Return the updated salt with its elements
        return prisma.salt.findUnique({
          where: { id: salt.id },
          include: {
            elements: {
              include: {
                element: true
              }
            }
          }
        });
      });
      
      return res.status(200).json(result);    } catch (error) {
      console.error(`Error updating salt: ${error}`);
      return res.status(500).json({ error: 'Failed to update salt' });
    }
  },

  // Delete salt
  deleteSalt: async (req: Request, res: Response) => {
    const { id } = req.params;
    
    try {
      // Check if the salt is used in any formulations
      const formulationSaltCount = await prisma.formulationSalt.count({
        where: { saltId: id }
      });
      
      if (formulationSaltCount > 0) {
        return res.status(409).json({ 
          error: 'Cannot delete salt as it is used in one or more formulations',
          count: formulationSaltCount
        });
      }
      
      // Use a transaction to delete salt elements and the salt
      await prisma.$transaction(async (prisma) => {
        // Delete all salt-element relations
        await prisma.saltElement.deleteMany({
          where: { saltId: id }
        });
        
        // Delete the salt
        await prisma.salt.delete({
          where: { id }
        });
      });
      
      return res.status(204).send();
    } catch (error) {
      console.error(`Error deleting salt: ${error}`);
      return res.status(500).json({ error: 'Failed to delete salt' });
    }
  },
  
  // Get custom salts
  getCustomSalts: async (_req: Request, res: Response) => {
    try {
      const customSalts = await prisma.salt.findMany({
        where: { isCustom: true },
        include: {
          elements: {
            include: {
              element: true
            }
          }
        }
      });
      
      return res.status(200).json(customSalts);
    } catch (error) {
      console.error(`Error fetching custom salts: ${error}`);
      return res.status(500).json({ error: 'Failed to fetch custom salts' });
    }
  },
  
  // Search salts
  searchSalts: async (req: Request, res: Response) => {
    const { term } = req.query;
    
    if (!term || typeof term !== 'string') {
      return res.status(400).json({ error: 'Search term is required' });
    }
    
    try {
      const salts = await prisma.salt.findMany({
        where: {
          OR: [
            { name: { contains: term } },
            { formula: { contains: term } }
          ]
        },
        include: {
          elements: {
            include: {
              element: true
            }
          }
        }
      });
      
      return res.status(200).json(salts);
    } catch (error) {
      console.error(`Error searching salts: ${error}`);
      return res.status(500).json({ error: 'Failed to search salts' });
    }
  }
};
