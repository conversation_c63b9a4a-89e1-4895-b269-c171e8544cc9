import { Request, Response } from 'express';
import WaterQuality from '../models/waterProfile';

// Get all water quality profiles
export const getAllWaterQualityProfiles = async (req: Request, res: Response) => {
    try {
        const profiles = await WaterQuality.find();
        res.status(200).json(profiles);
    } catch (error) {
        res.status(500).json({ message: 'Error retrieving water quality profiles', error });
    }
};

// Create a new water quality profile
export const createWaterQualityProfile = async (req: Request, res: Response) => {
    const newProfile = new WaterQuality(req.body);
    try {
        const savedProfile = await newProfile.save();
        res.status(201).json(savedProfile);
    } catch (error) {
        res.status(400).json({ message: 'Error creating water quality profile', error });
    }
};

// Update a water quality profile
export const updateWaterQualityProfile = async (req: Request, res: Response) => {
    const { id } = req.params;
    try {
        const updatedProfile = await WaterQuality.findByIdAndUpdate(id, req.body, { new: true });
        if (!updatedProfile) {
            return res.status(404).json({ message: 'Water quality profile not found' });
        }
        res.status(200).json(updatedProfile);
    } catch (error) {
        res.status(400).json({ message: 'Error updating water quality profile', error });
    }
};

// Delete a water quality profile
export const deleteWaterQualityProfile = async (req: Request, res: Response) => {
    const { id } = req.params;
    try {
        const deletedProfile = await WaterQuality.findByIdAndDelete(id);
        if (!deletedProfile) {
            return res.status(404).json({ message: 'Water quality profile not found' });
        }
        res.status(204).send();
    } catch (error) {
        res.status(500).json({ message: 'Error deleting water quality profile', error });
    }
};