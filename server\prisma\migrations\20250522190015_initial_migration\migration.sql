-- CreateTable
CREATE TABLE "Element" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "symbol" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "atomicWeight" REAL NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "Salt" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "name" TEXT NOT NULL,
    "formula" TEXT,
    "isCustom" BOOLEAN NOT NULL DEFAULT false,
    "density" REAL,
    "solubility" REAL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "SaltElement" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "saltId" TEXT NOT NULL,
    "elementId" TEXT NOT NULL,
    "percentage" REAL NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "SaltElement_saltId_fkey" FOREIGN KEY ("saltId") REFERENCES "Salt" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "SaltElement_elementId_fkey" FOREIGN KEY ("elementId") REFERENCES "Element" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "WaterProfile" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "ec" REAL NOT NULL,
    "ph" REAL NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "WaterElement" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "waterProfileId" TEXT NOT NULL,
    "elementSymbol" TEXT NOT NULL,
    "concentration" REAL NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "WaterElement_waterProfileId_fkey" FOREIGN KEY ("waterProfileId") REFERENCES "WaterProfile" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "Formulation" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "waterProfileId" TEXT NOT NULL,
    "targetEc" REAL,
    "targetPh" REAL,
    "volume" REAL NOT NULL,
    "volumeUnit" TEXT NOT NULL DEFAULT 'L',
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "Formulation_waterProfileId_fkey" FOREIGN KEY ("waterProfileId") REFERENCES "WaterProfile" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "FormulationSalt" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "formulationId" TEXT NOT NULL,
    "saltId" TEXT NOT NULL,
    "amount" REAL NOT NULL,
    "unit" TEXT NOT NULL DEFAULT 'g',
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "FormulationSalt_formulationId_fkey" FOREIGN KEY ("formulationId") REFERENCES "Formulation" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "FormulationSalt_saltId_fkey" FOREIGN KEY ("saltId") REFERENCES "Salt" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "TargetElement" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "formulationId" TEXT NOT NULL,
    "elementId" TEXT NOT NULL,
    "concentration" REAL NOT NULL,
    "unit" TEXT NOT NULL DEFAULT 'ppm',
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "TargetElement_formulationId_fkey" FOREIGN KEY ("formulationId") REFERENCES "Formulation" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "TargetElement_elementId_fkey" FOREIGN KEY ("elementId") REFERENCES "Element" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateIndex
CREATE UNIQUE INDEX "Element_symbol_key" ON "Element"("symbol");

-- CreateIndex
CREATE UNIQUE INDEX "SaltElement_saltId_elementId_key" ON "SaltElement"("saltId", "elementId");

-- CreateIndex
CREATE UNIQUE INDEX "WaterElement_waterProfileId_elementSymbol_key" ON "WaterElement"("waterProfileId", "elementSymbol");

-- CreateIndex
CREATE UNIQUE INDEX "FormulationSalt_formulationId_saltId_key" ON "FormulationSalt"("formulationId", "saltId");

-- CreateIndex
CREATE UNIQUE INDEX "TargetElement_formulationId_elementId_key" ON "TargetElement"("formulationId", "elementId");
