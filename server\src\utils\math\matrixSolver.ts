/**
 * Matrix Solver utility
 * Ported from HydroBuddy's densesolver.pas
 */

export interface Matrix {
  rows: number;
  cols: number;
  data: number[][];
}

export interface Vector {
  length: number;
  data: number[];
}

/**
 * Creates a new matrix with the specified dimensions
 */
export function createMatrix(rows: number, cols: number): Matrix {
  const data: number[][] = [];
  for (let i = 0; i < rows; i++) {
    data.push(Array(cols).fill(0));
  }
  return { rows, cols, data };
}

/**
 * Creates a new vector with the specified length
 */
export function createVector(length: number): Vector {
  return {
    length,
    data: Array(length).fill(0)
  };
}

/**
 * Solves a system of linear equations using Gaussian elimination
 * Analogous to the DenseSolverRMatrixSolve function in HydroBuddy
 * @param a - Coefficient matrix
 * @param b - Right-hand side vector
 * @returns Solution vector
 */
export function solveLinearSystem(a: Matrix, b: Vector): Vector {
  if (a.rows !== a.cols) {
    throw new Error('Matrix must be square');
  }
  
  if (a.rows !== b.length) {
    throw new Error('Matrix dimensions must match vector length');
  }
  
  const n = a.rows;
  
  // Create copies to avoid modifying the originals
  const aCopy = JSON.parse(JSON.stringify(a)) as Matrix;
  const bCopy = JSON.parse(JSON.stringify(b)) as Vector;
  
  // Forward elimination
  for (let k = 0; k < n; k++) {
    // Find pivot
    let pivotIndex = k;
    let pivotValue = Math.abs(aCopy.data[k][k]);
    
    for (let i = k + 1; i < n; i++) {
      const absValue = Math.abs(aCopy.data[i][k]);
      if (absValue > pivotValue) {
        pivotIndex = i;
        pivotValue = absValue;
      }
    }
    
    // Swap rows if needed
    if (pivotIndex !== k) {
      // Swap rows in matrix
      [aCopy.data[k], aCopy.data[pivotIndex]] = [aCopy.data[pivotIndex], aCopy.data[k]];
      // Swap elements in vector
      [bCopy.data[k], bCopy.data[pivotIndex]] = [bCopy.data[pivotIndex], bCopy.data[k]];
    }
    
    // Check for singular matrix
    if (Math.abs(aCopy.data[k][k]) < 1e-13) {
      throw new Error('Matrix is singular or nearly singular');
    }
    
    // Elimination
    for (let i = k + 1; i < n; i++) {
      const factor = aCopy.data[i][k] / aCopy.data[k][k];
      bCopy.data[i] -= factor * bCopy.data[k];
      
      for (let j = k; j < n; j++) {
        aCopy.data[i][j] -= factor * aCopy.data[k][j];
      }
    }
  }
  
  // Back substitution
  const result = createVector(n);
  for (let i = n - 1; i >= 0; i--) {
    let sum = 0;
    for (let j = i + 1; j < n; j++) {
      sum += aCopy.data[i][j] * result.data[j];
    }
    result.data[i] = (bCopy.data[i] - sum) / aCopy.data[i][i];
  }
  
  return result;
}

/**
 * Calculate the determinant of a matrix using LU decomposition
 */
export function determinant(a: Matrix): number {
  if (a.rows !== a.cols) {
    throw new Error('Matrix must be square');
  }
  
  const n = a.rows;
  const aCopy = JSON.parse(JSON.stringify(a)) as Matrix;
  let det = 1;
  let sign = 1;
  
  // Forward elimination
  for (let k = 0; k < n; k++) {
    // Find pivot
    let pivotIndex = k;
    let pivotValue = Math.abs(aCopy.data[k][k]);
    
    for (let i = k + 1; i < n; i++) {
      const absValue = Math.abs(aCopy.data[i][k]);
      if (absValue > pivotValue) {
        pivotIndex = i;
        pivotValue = absValue;
      }
    }
    
    // Swap rows if needed
    if (pivotIndex !== k) {
      [aCopy.data[k], aCopy.data[pivotIndex]] = [aCopy.data[pivotIndex], aCopy.data[k]];
      sign = -sign; // Change sign of determinant
    }
    
    // Check for singular matrix
    if (Math.abs(aCopy.data[k][k]) < 1e-13) {
      return 0; // Singular matrix has determinant 0
    }
    
    // Multiplication of diagonal elements
    det *= aCopy.data[k][k];
    
    // Elimination
    for (let i = k + 1; i < n; i++) {
      const factor = aCopy.data[i][k] / aCopy.data[k][k];
      
      for (let j = k + 1; j < n; j++) {
        aCopy.data[i][j] -= factor * aCopy.data[k][j];
      }
    }
  }
  
  return sign * det;
}
