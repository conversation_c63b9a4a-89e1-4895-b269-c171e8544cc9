import React, { useEffect, useRef, useState } from 'react';
import { 
  <PERSON>, 
  Card, 
  CardContent, 
  CardHeader, 
  Grid, 
  Typography, 
  useTheme,
  Paper,
  Tooltip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  IconButton
} from '@mui/material';
import { 
  PieChart, 
  Pie, 
  Cell, 
  ResponsiveContainer, 
  Sector, 
  Legend,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip
} from 'recharts';
import FileDownloadIcon from '@mui/icons-material/FileDownload';

interface ElementComposition {
  symbol: string;
  percentage: number;
  name?: string;
  color?: string;
}

interface SaltCompositionProps {
  saltName: string;
  formula?: string;
  elements: ElementComposition[];
}

// Element color mapping
const ELEMENT_COLORS = {
  N: '#3498db',   // Nitrogen - blue
  P: '#f39c12',   // Phosphorus - orange
  K: '#9b59b6',   // Potassium - purple
  Ca: '#e74c3c',  // Calcium - red
  Mg: '#2ecc71',  // Magnesium - green
  S: '#f1c40f',   // Sulfur - yellow
  Fe: '#e67e22',  // Iron - rust
  Mn: '#95a5a6',  // Manganese - gray
  Zn: '#1abc9c',  // Zinc - teal
  Cu: '#34495e',  // Copper - dark blue
  B: '#7f8c8d',   // Boron - dark gray
  Mo: '#d35400',  // Molybdenum - dark orange
  Cl: '#16a085',  // Chlorine - green blue
  Na: '#27ae60',  // Sodium - light green
  Si: '#8e44ad',  // Silicon - dark purple
  Co: '#c0392b',  // Cobalt - dark red
  // Default color for other elements
  default: '#bdc3c7' // light gray
};

const SaltCompositionAnalysis: React.FC<SaltCompositionProps> = ({ 
  saltName,
  formula,
  elements
}) => {
  const theme = useTheme();
  const [activeIndex, setActiveIndex] = useState<number | undefined>(undefined);
  const [chartData, setChartData] = useState<ElementComposition[]>([]);

  // Prepare chart data
  useEffect(() => {
    // Sort elements by percentage (descending)
    const sortedElements = [...elements].sort((a, b) => b.percentage - a.percentage);
    
    // Assign colors to elements
    const coloredElements = sortedElements.map(elem => ({
      ...elem,
      color: (ELEMENT_COLORS as any)[elem.symbol] || ELEMENT_COLORS.default
    }));
    
    setChartData(coloredElements);
  }, [elements]);

  const onPieEnter = (_: any, index: number) => {
    setActiveIndex(index);
  };
  
  const onPieLeave = () => {
    setActiveIndex(undefined);
  };

  // Custom active shape for pie chart
  const renderActiveShape = (props: any) => {
    const { cx, cy, innerRadius, outerRadius, startAngle, endAngle, fill, payload, value } = props;
    
    return (
      <g>
        <Sector
          cx={cx}
          cy={cy}
          innerRadius={innerRadius}
          outerRadius={outerRadius + 10}
          startAngle={startAngle}
          endAngle={endAngle}
          fill={fill}
        />
        <Sector
          cx={cx}
          cy={cy}
          startAngle={startAngle}
          endAngle={endAngle}
          innerRadius={outerRadius + 15}
          outerRadius={outerRadius + 18}
          fill={fill}
        />
        <text x={cx} y={cy} textAnchor="middle" dominantBaseline="central">
          <tspan x={cx} y={cy-15} textAnchor="middle" fill="#333" fontWeight="bold">
            {payload.symbol}
          </tspan>
          <tspan x={cx} y={cy+15} textAnchor="middle" fontSize="14" fill="#333">
            {value.toFixed(1)}%
          </tspan>
        </text>
      </g>
    );
  };

  return (
    <Card>
      <CardHeader 
        title={`${saltName} Composition Analysis`} 
        subheader={formula ? `Chemical Formula: ${formula}` : 'Element Breakdown'}
      />      <CardContent>
        <Grid container spacing={3}>
          {/* Visualization Section */}
          <Grid size={{ xs: 12, md: 6 }}>
            <Box sx={{ height: 300, width: '100%' }}>
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={chartData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    activeIndex={activeIndex}
                    activeShape={renderActiveShape}
                    onMouseEnter={onPieEnter}
                    onMouseLeave={onPieLeave}
                    dataKey="percentage"
                    nameKey="symbol"
                    innerRadius={60}
                    outerRadius={80}
                  >
                    {chartData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </Box>
          </Grid>

          {/* Composition Table Section */}
          <Grid size={{ xs: 12, md: 6 }}>
            <Paper elevation={1} sx={{ p: 1 }}>
              <Typography variant="subtitle1" gutterBottom>
                Element Composition
              </Typography>
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Element</TableCell>
                      <TableCell>Symbol</TableCell>
                      <TableCell align="right">Percentage (%)</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {chartData.map((element) => (
                      <TableRow key={element.symbol}>
                        <TableCell>{element.name || 'Unknown'}</TableCell>
                        <TableCell>
                          <Box 
                            component="span" 
                            sx={{ 
                              display: 'inline-block',
                              width: 16,
                              height: 16,
                              borderRadius: '50%',
                              bgcolor: element.color,
                              mr: 1,
                              verticalAlign: 'middle'
                            }} 
                          />
                          {element.symbol}
                        </TableCell>
                        <TableCell align="right">{element.percentage.toFixed(2)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>
            
            {/* Stats Section */}
            <Box sx={{ mt: 2 }}>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Paper elevation={1} sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="body2" color="text.secondary">
                      Primary Element
                    </Typography>
                    <Typography variant="h6">
                      {chartData.length > 0 ? `${chartData[0].symbol} (${chartData[0].percentage.toFixed(1)}%)` : 'N/A'}
                    </Typography>
                  </Paper>
                </Grid>
                <Grid item xs={6}>
                  <Paper elevation={1} sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="body2" color="text.secondary">
                      Total Elements
                    </Typography>
                    <Typography variant="h6">
                      {chartData.length}
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>
            </Box>
          </Grid>
          
          {/* Nutrient Classification */}
          <Grid item xs={12}>
            <Paper elevation={1} sx={{ p: 2, mt: 1 }}>
              <Typography variant="subtitle1" gutterBottom>
                Nutrient Classification
              </Typography>
              <Grid container spacing={2}>
                {['N', 'P', 'K'].some(symbol => chartData.some(e => e.symbol === symbol)) && (
                  <Grid item xs={12} sm={4}>
                    <Paper variant="outlined" sx={{ p: 1, textAlign: 'center' }}>
                      <Typography variant="body2" color="text.secondary">
                        Macronutrients (N-P-K)
                      </Typography>
                      <Typography variant="h6">
                        {['N', 'P', 'K'].map(symbol => {
                          const elem = chartData.find(e => e.symbol === symbol);
                          return elem ? elem.percentage.toFixed(1) : '0';
                        }).join('-')}
                      </Typography>
                    </Paper>
                  </Grid>
                )}
                
                {['Ca', 'Mg', 'S'].some(symbol => chartData.some(e => e.symbol === symbol)) && (
                  <Grid item xs={12} sm={4}>
                    <Paper variant="outlined" sx={{ p: 1, textAlign: 'center' }}>
                      <Typography variant="body2" color="text.secondary">
                        Secondary Nutrients
                      </Typography>
                      <Typography variant="body1">
                        {['Ca', 'Mg', 'S'].map(symbol => {
                          const elem = chartData.find(e => e.symbol === symbol);
                          return elem ? `${symbol}: ${elem.percentage.toFixed(1)}%` : '';
                        }).filter(Boolean).join(', ')}
                      </Typography>
                    </Paper>
                  </Grid>
                )}
                
                {['Fe', 'Mn', 'Zn', 'Cu', 'B', 'Mo', 'Cl'].some(symbol => chartData.some(e => e.symbol === symbol)) && (
                  <Grid item xs={12} sm={4}>
                    <Paper variant="outlined" sx={{ p: 1, textAlign: 'center' }}>
                      <Typography variant="body2" color="text.secondary">
                        Micronutrients
                      </Typography>
                      <Typography variant="body1">
                        {['Fe', 'Mn', 'Zn', 'Cu', 'B', 'Mo', 'Cl'].map(symbol => {
                          const elem = chartData.find(e => e.symbol === symbol);
                          return elem ? `${symbol}: ${elem.percentage.toFixed(1)}%` : '';
                        }).filter(Boolean).join(', ')}
                      </Typography>
                    </Paper>
                  </Grid>
                )}
              </Grid>
            </Paper>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
};

export default SaltCompositionAnalysis;
