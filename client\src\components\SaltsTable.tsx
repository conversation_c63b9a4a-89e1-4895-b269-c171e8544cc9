import React, { useEffect, useState } from 'react';
import axios from 'axios';
import {
  Box,
  Checkbox,
  FormControlLabel,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  TextField,
  InputAdornment,
  CircularProgress,
  Button,
  Alert
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import RefreshIcon from '@mui/icons-material/Refresh';

interface ElementComposition {
  elementSymbol: string;
  percentage: number;
  element?: {
    symbol: string;
    name: string;
  }
}

interface Salt {
  id: string;
  name: string;
  formula: string;
  description?: string;
  isCustom: boolean;
  elements: ElementComposition[];
}

interface SaltsTableProps {
  onSaltSelect?: (salt: Salt) => void;
}

const SaltsTable: React.FC<SaltsTableProps> = ({ onSaltSelect }) => {
  const [salts, setSalts] = useState<Salt[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedSalt, setSelectedSalt] = useState<Salt | null>(null);
  const [showCustomOnly, setShowCustomOnly] = useState<boolean>(false);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const fetchSalts = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const url = showCustomOnly 
        ? 'http://localhost:3001/api/salts/custom' 
        : 'http://localhost:3001/api/salts';
      
      const response = await axios.get(url, {
        params: {
          includeElements: 'true'
        },
        timeout: 10000 // 10 second timeout
      });
      
      setSalts(response.data);
      setError(null);
    } catch (err: any) {
      if (axios.isAxiosError(err)) {
        if (err.code === 'ECONNABORTED') {
          setError('Request timed out. Server might be unavailable.');
        } else if (err.response) {
          // Server responded with error status
          setError(`Server error: ${err.response.status} - ${err.response.statusText}`);
        } else if (err.request) {
          // Request made but no response received
          setError('No response from the server. Please check your connection.');
        } else {
          setError(`Error fetching salts: ${err.message}`);
        }
      } else {
        setError('Failed to fetch salts. Please try again later.');
      }
      console.error('Error fetching salts:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSalts();
  }, [showCustomOnly]);

  const handleSaltClick = (salt: Salt) => {
    setSelectedSalt(salt);
    if (onSaltSelect) {
      onSaltSelect(salt);
    }
  };

  const toggleCustomOnly = () => {
    setShowCustomOnly(!showCustomOnly);
  };
  
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };
  
  // Filter salts based on search term
  const filteredSalts = salts.filter(salt => 
    salt.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
    (salt.formula && salt.formula.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  if (error) {
    return (
      <Paper sx={{ p: 3, textAlign: 'center' }}>
        <Typography color="error">{error}</Typography>
      </Paper>
    );
  }

  return (
    <Paper elevation={1} sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', alignItems: 'center', gap: 2, justifyContent: 'space-between' }}>
          <TextField
            label="Search Salts"
            value={searchTerm}
            onChange={handleSearchChange}
            variant="outlined"
            size="small"
            sx={{ minWidth: '200px' }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
          />
          <FormControlLabel
            control={
              <Checkbox 
                checked={showCustomOnly}
                onChange={toggleCustomOnly}
                color="primary"
              />
            }
            label="Show Custom Salts Only"
          />
        </Box>
          
        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
          {filteredSalts.length} {filteredSalts.length === 1 ? 'salt' : 'salts'} found
        </Typography>
          {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Alert 
              severity="error" 
              sx={{ mb: 2 }}
              action={
                <Button 
                  color="inherit" 
                  size="small"
                  startIcon={<RefreshIcon />}
                  onClick={fetchSalts}
                >
                  Retry
                </Button>
              }
            >
              {error}
            </Alert>
          </Box>
        ) : (
          <TableContainer component={Paper} variant="outlined">
            <Table size="medium">
              <TableHead sx={{ bgcolor: 'background.default' }}>
                <TableRow>
                  <TableCell><strong>Name</strong></TableCell>
                  <TableCell><strong>Formula</strong></TableCell>
                  <TableCell><strong>Elements</strong></TableCell>
                  <TableCell><strong>Type</strong></TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredSalts.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={4} align="center" sx={{ py: 3 }}>
                      <Typography variant="body1" color="text.secondary">
                        {searchTerm ? 'No matching salts found' : 'No salts available'}
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredSalts.map((salt) => (
                    <TableRow 
                      key={salt.id} 
                      onClick={() => handleSaltClick(salt)}
                      selected={selectedSalt?.id === salt.id}
                      hover
                      sx={{ 
                        cursor: 'pointer',
                        '&.Mui-selected': { bgcolor: 'action.selected' }
                      }}
                    >
                      <TableCell>{salt.name}</TableCell>
                      <TableCell>{salt.formula || '—'}</TableCell>
                      <TableCell>
                        {salt.elements?.length > 0 
                          ? salt.elements
                              .slice(0, 3)
                              .map(e => e.elementSymbol || (e.element && e.element.symbol) || '')
                              .join(', ') + (salt.elements.length > 3 ? '...' : '')
                          : '—'
                        }
                      </TableCell>
                      <TableCell>{salt.isCustom ? 'Custom' : 'Standard'}</TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </Box>
    </Paper>
  );
};

export default SaltsTable;
