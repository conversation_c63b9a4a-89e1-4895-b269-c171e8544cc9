<!-- @format -->

# HydroBuddy SPA Conversion: Professional End-to-End Plan

This document lays out a comprehensive, actionable roadmap for converting the Pascal-based HydroBuddy application into a robust Single-Page Application (SPA). It provides professional recommendations, detailed phases, module mappings, technology choices, and success metrics.

---

## 1. Executive Summary

- **Objective**: Migrate HydroBuddy’s hydroponic nutrient calculation features from a desktop Pascal application into a modern web SPA
- **Scope**: All existing core functionalities (data management, calculation engine, solution preparation, reporting) plus user/auth management, monitoring, and performance optimization
- **Approach**: Phased delivery with iterative feedback loops; component-driven SPA front-end; RESTful microservices back-end

---

## 2. SPA Architecture Overview

- **Framework**: React (Vite) or Vue 3 (Vite) for fast builds and hot-reload
- **State Management**: Redux Toolkit or Pinia for centralized data flows
- **Routing**: React Router or Vue Router for view navigation
- **UI Library**: Material UI (React) or Vuetify (Vue) for professional, responsive components
- **Charting**: Chart.js + D3 for flexible visualization
- **Build Tools**: Vite for front-end, Docker for containerization

---

## 3. Backend Microservices

- **Language & Framework**: Node.js + Express (TypeScript) or Python + FastAPI
- **Database**: PostgreSQL
- **ORM**: TypeORM or Prisma (Node.js) / SQLAlchemy (Python)
- **Auth**: JWT-based with refresh tokens; RBAC for form management
- **API Design**: RESTful endpoints grouped by feature

  - `/api/water-quality` (CRUD)
  - `/api/formulations` (CRUD)
  - `/api/salts` (CRUD)
  - `/api/calculate/*` (stateless calculation endpoints)

- **Testing**: Jest (Node.js) or PyTest (Python) for unit & integration tests

---

## 4. Phased Delivery Plan

| Phase                           | Duration | Deliverables                                                        | Success Criteria                                 |
| ------------------------------- | -------- | ------------------------------------------------------------------- | ------------------------------------------------ |
| **1. Planning & Prototyping**   | 2w       | - Detailed spec & wireframes<br>- Proof-of-concept SPA shell        | Stakeholder sign-off on specs & POC              |
| **2. Data & Auth**              | 3w       | - PostgreSQL schema<br>- DB migration scripts<br>- Auth service     | CRUD tested; Auth flows operational              |
| **3. Core Calculations API**    | 4w       | - Endpoints for stoich, scale, precision<br>- Unit tests vs. Pascal | Calculations match Pascal outputs (±0.1%)        |
| **4. SPA Basic UI**             | 4w       | - React/Vue shell with form pages<br>- State & routing              | Forms validate correctly; UI matches wireframes  |
| **5. Results Visualization**    | 3w       | - Dashboard with tables & charts<br>- Export CSV/PDF                | Charts display data accurately; export verified  |
| **6. Advanced Modules**         | 3w       | - pH, ratios, commercial mix, tissue panels                         | Advanced results match desktop outputs           |
| **7. E2E Testing & Monitoring** | 3w       | - Cypress tests<br>- Sentry & Prometheus setup<br>- CI/CD pipeline  | 90%+ test coverage; monitored in staging         |
| **8. Launch & Optimization**    | 2w       | - Production deployment<br>- Performance tuning                     | Response times <500ms; CPU/memory within targets |

---

## 5. Module Mapping & Responsibilities

| Legacy Unit               | API Endpoint                            | Frontend Component         | Team/Owner       |
| ------------------------- | --------------------------------------- | -------------------------- | ---------------- |
| hb_waterquality.pas       | GET/POST `/api/water-quality`           | `<WaterQualityForm />`     | Backend/Frontend |
| hb_load_salts.pas         | GET `/api/salts`                        | `<SaltLibrary />`          | Backend/Frontend |
| hb_newcustomsalt.pas      | POST `/api/salts`                       | `<CustomSaltEditor />`     | Backend/Frontend |
| densesolver.pas           | POST `/api/calculate/stoich`            | CalculationService         | Backend          |
| hb_addweight.pas          | POST `/api/calculate/scale`             | `<BatchPlanner />`         | Backend/Frontend |
| hb_insprecision.pas       | POST `/api/calculate/precision`         | `<PrecisionTuner />`       | Backend/Frontend |
| hb_stockanalysis.pas      | POST `/api/calculate/stock-plan`        | `<StockSolutionPlanner />` | Backend/Frontend |
| hb_commercialnutrient.pas | POST `/api/calculate/commercial-mix`    | `<CommercialResults />`    | Backend/Frontend |
| hb_persubstance.pas       | POST `/api/calculate/breakdown`         | `<BreakdownMatrix />`      | Backend/Frontend |
| hb_ph.pas                 | POST `/api/calculate/ph`                | `<PHEstimator />`          | Backend/Frontend |
| hb_ratios.pas             | POST `/api/calculate/ratios`            | `<RatioDashboard />`       | Backend/Frontend |
| hb_comparison.pas         | POST `/api/calculate/compare-standards` | `<StandardsComparator />`  | Backend/Frontend |
| hb_tissue_analysis.pas    | POST `/api/calculate/tissue-analysis`   | `<TissueAnalysisPanel />`  | Backend/Frontend |

---

## 6. Data Flow Diagram

1. **UI Layer** collects water quality, targets, options → updates Redux/Pinia store.
2. **Calculation Trigger** calls `/api/calculate/all` with aggregated payload.
3. **API Gateway** routes to microservices:

   - Validation Service → `WaterQualityService` → `FormulationService` → `CalculationService` → `ResultAssembler`

4. **Result Object** returned to client:

   ```json
   {
     "saltMasses": [...],
     "elementConcentrations": [...],
     "pH": 5.8,
     "ratios": [...],
     "commercialMix": [...]
   }
   ```

5. **Frontend** renders tables/charts, caches state for re-runs.

---

## 7. Monitoring & Quality

- **Error Tracking**: Sentry captures JS exceptions and API errors.
- **Metrics**: Prometheus exporter for request latency, calculation time, DB query times.
- **Logs**: Structured JSON logs to Loki; UI allows filtering.
- **Testing**:

  - **Unit**: 90%+ coverage for calculation modules.
  - **Integration**: Jest/ Mocha testing endpoints.
  - **E2E**: Cypress covering user flows.

---

## 8. Success Metrics

- **Accuracy**: Web calculations within 1% of Pascal results on key test vectors.
- **Performance**: <200 ms calc time for typical 10-element, 12-salt load (median).
- **Reliability**: <0.1% error rate in logs.
- **Adoption**: Migrated 100% of active HydroBuddy users to web by launch +1 month.

---

_End of HydroBuddy SPA Conversion Plan_
