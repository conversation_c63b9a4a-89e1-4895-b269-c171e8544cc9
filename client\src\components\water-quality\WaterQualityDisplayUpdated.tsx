import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  Grid,
  Paper,
  Typography,
  Divider,
  Chip,
  Button,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Snackbar,
  Alert,
  CircularProgress,
  Box
} from '@mui/material';
import { exportToCsv, exportToJson, formatDateForFilename } from '../../utils/exportUtils';
import { WaterQualityData } from '../../store/slices/waterQualitySlice';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import TableRowsIcon from '@mui/icons-material/TableRows';
import CodeIcon from '@mui/icons-material/Code';
import ShareIcon from '@mui/icons-material/Share';
import PrintIcon from '@mui/icons-material/Print';

interface WaterQualityDisplayProps {
  waterData: WaterQualityData;
  ionUnit?: 'ppm' | 'meq' | 'mM';
  onEdit?: () => void;
}

const WaterQualityDisplayUpdated: React.FC<WaterQualityDisplayProps> = ({
  waterData,
  ionUnit = 'ppm',
  onEdit
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error' | 'warning' | 'info'>('success');
  const [isLoading, setIsLoading] = useState(false);

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  const showNotification = (message: string, severity: 'success' | 'error' | 'warning' | 'info' = 'success') => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setSnackbarOpen(true);
  };

  // Prepare export data with robust formatting
  const prepareExportData = () => {
    // Calculate hardness values
    const dGH = calculateDGH(waterData.ca, waterData.mg);
    const dKH = calculateDKH(waterData.hco3, waterData.co3);
    const hardnessCategory = getWaterHardnessCategory(dGH);
    const alkalinityCategory = getAlkalinityCategory(dKH);

    const waterProfileInfo = {
      name: waterData.name || 'Water Quality Profile',
      timestamp: new Date().toISOString(),
      basicParameters: {
        ph: waterData.ph,
        ec: {
          value: waterData.ec,
          unit: 'mS/cm'
        },
        tds: {
          value: waterData.tds,
          unit: 'ppm'
        },
        temperature: {
          value: waterData.temperature,
          unit: '°C'
        }
      },
      hardness: {
        germanHardness: {
          value: dGH,
          unit: '°dH'
        },
        category: hardnessCategory.label
      },
      alkalinity: {
        carbonateHardness: {
          value: dKH,
          unit: '°dH'
        },
        category: alkalinityCategory.label
      },
      ionComposition: {
        unit: ionUnit,
        cations: {
          calcium: waterData.ca,
          magnesium: waterData.mg,
          potassium: waterData.k,
          sodium: waterData.na
        },
        anions: {
          chloride: waterData.cl,
          sulfate: waterData.so4,
          nitrate: waterData.no3,
          bicarbonate: waterData.hco3,
          carbonate: waterData.co3
        }
      }
    };

    return waterProfileInfo;
  };

  // Export as CSV with enhanced formatting
  const handleExportCsv = async () => {
    setIsLoading(true);
    try {
      const data = prepareExportData();

      // Create table headers and rows
      const headers = ['Property', 'Value', 'Unit', 'Notes'];

      // Add basic parameters rows with clear sections
      const basicParamRows = [
        ['--- Basic Parameters ---', '', '', ''],
        ['Profile Name', data.name, '', ''],
        ['pH', data.basicParameters.ph.toString(), '', ''],
        ['EC', data.basicParameters.ec.value.toString(), data.basicParameters.ec.unit, ''],
        ['TDS', data.basicParameters.tds.value.toString(), data.basicParameters.tds.unit, ''],
        ['Temperature', data.basicParameters.temperature.value.toString(), data.basicParameters.temperature.unit, ''],
        ['', '', '', ''] // Empty row as separator
      ];

      // Add hardness section
      const hardnessRows = [
        ['--- Water Hardness ---', '', '', ''],
        ['German Hardness (dGH)', data.hardness.germanHardness.value.toString(), data.hardness.germanHardness.unit, ''],
        ['Hardness Category', data.hardness.category, '', ''],
        ['Carbonate Hardness (dKH)', data.alkalinity.carbonateHardness.value.toString(), data.alkalinity.carbonateHardness.unit, ''],
        ['Alkalinity Category', data.alkalinity.category, '', ''],
        ['', '', '', ''] // Empty row as separator
      ];

      // Add ion composition with clear sections
      const ionUnit = data.ionComposition.unit;
      const ionRows = [
        [`--- Ion Composition (${ionUnit}) ---`, '', '', ''],
        ['Cations:', '', '', ''],
        ['Calcium (Ca²⁺)', data.ionComposition.cations.calcium.toString(), ionUnit, ''],
        ['Magnesium (Mg²⁺)', data.ionComposition.cations.magnesium.toString(), ionUnit, ''],
        ['Potassium (K⁺)', data.ionComposition.cations.potassium.toString(), ionUnit, ''],
        ['Sodium (Na⁺)', data.ionComposition.cations.sodium.toString(), ionUnit, ''],
        ['', '', '', ''],
        ['Anions:', '', '', ''],
        ['Chloride (Cl⁻)', data.ionComposition.anions.chloride.toString(), ionUnit, ''],
        ['Sulfate (SO₄²⁻)', data.ionComposition.anions.sulfate.toString(), ionUnit, ''],
        ['Nitrate (NO₃⁻)', data.ionComposition.anions.nitrate.toString(), ionUnit, ''],
        ['Bicarbonate (HCO₃⁻)', data.ionComposition.anions.bicarbonate.toString(), ionUnit, ''],
        ['Carbonate (CO₃²⁻)', data.ionComposition.anions.carbonate.toString(), ionUnit, '']
      ];

      // Add metadata
      const metadataRows = [
        ['', '', '', ''],
        ['Export Date', formatDateForFilename(), '', ''],
        ['Generated By', 'FlahaFast Hydroponics Application', '', '']
      ];

      // Combine all rows
      const allRows = [...basicParamRows, ...hardnessRows, ...ionRows, ...metadataRows];

      // Export with robust filename handling
      const safeFilename = `${data.name.replace(/\s+/g, '_').replace(/[^a-zA-Z0-9_-]/g, '')}_water_quality_${formatDateForFilename()}`;

      // Add small delay to show loading indicator
      await new Promise(resolve => setTimeout(resolve, 500));

      exportToCsv(headers, allRows, safeFilename);
      handleMenuClose();
      showNotification('Water quality data exported as CSV successfully', 'success');
    } catch (error) {
      console.error('Error exporting to CSV:', error);
      showNotification('Failed to export water quality data as CSV', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // Export as JSON with robust handling
  const handleExportJson = async () => {
    setIsLoading(true);
    try {
      const data = prepareExportData();

      const exportData = {
        waterProfile: data,
        metadata: {
          exportDate: new Date().toISOString(),
          application: 'FlahaFast Hydroponics Application',
          version: '1.0'
        }
      };

      // Add small delay to show loading indicator
      await new Promise(resolve => setTimeout(resolve, 500));

      // Export with robust filename handling
      const safeFilename = `${data.name.replace(/\s+/g, '_').replace(/[^a-zA-Z0-9_-]/g, '')}_water_quality_${formatDateForFilename()}`;
      exportToJson(exportData, safeFilename);

      handleMenuClose();
      showNotification('Water quality data exported as JSON successfully', 'success');
    } catch (error) {
      console.error('Error exporting to JSON:', error);
      showNotification('Failed to export water quality data as JSON', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // Print water profile
  const handlePrint = () => {
    handleMenuClose();
    window.print();
  };

  // Share water profile (demo functionality)
  const handleShare = () => {
    handleMenuClose();

    if (navigator.share) {
      const data = prepareExportData();
      navigator.share({
        title: `${data.name} - Water Quality Profile`,
        text: `Water quality profile data for ${data.name}`,
        // URL would typically point to a shareable web page
        url: window.location.href
      })
      .then(() => showNotification('Shared successfully'))
      .catch((error) => {
        console.error('Error sharing:', error);
        showNotification('Failed to share water profile', 'error');
      });
    } else {
      showNotification('Sharing is not supported on this device', 'info');
    }
  };

  // Utility functions for calculations
  const calculateDGH = (ca: number, mg: number): number => {
    if (ionUnit === 'ppm') {
      return Math.round(((ca / 7.14) + (mg / 4.33)) * 10) / 10;
    } else if (ionUnit === 'meq') {
      return Math.round((ca * 2.5 + mg * 4.1) * 10) / 10;
    } else {
      return Math.round((ca * 5 + mg * 8.2) * 10) / 10;
    }
  };

  const calculateDKH = (hco3: number, co3: number): number => {
    if (ionUnit === 'ppm') {
      return Math.round(((hco3 / 21.8) + (co3 / 10.9)) * 10) / 10;
    } else if (ionUnit === 'meq') {
      return Math.round((hco3 + co3 * 2) * 10) / 10;
    } else {
      return Math.round((hco3 + co3 * 2) * 10) / 10;
    }
  };

  const getWaterHardnessCategory = (dGH: number): {
    label: string;
    color: "success" | "info" | "warning" | "error" | "default" | "primary" | "secondary";
  } => {
    if (dGH < 4) {
      return { label: "Soft", color: "success" };
    } else if (dGH < 8) {
      return { label: "Moderately Soft", color: "info" };
    } else if (dGH < 12) {
      return { label: "Moderately Hard", color: "warning" };
    } else if (dGH < 18) {
      return { label: "Hard", color: "error" };
    } else {
      return { label: "Very Hard", color: "error" };
    }
  };

  const getAlkalinityCategory = (dKH: number): {
    label: string;
    color: "success" | "info" | "warning" | "error" | "default" | "primary" | "secondary";
  } => {
    if (dKH < 1) {
      return { label: "Very Low", color: "error" };
    } else if (dKH < 3) {
      return { label: "Low", color: "warning" };
    } else if (dKH < 6) {
      return { label: "Moderate", color: "info" };
    } else if (dKH < 10) {
      return { label: "High", color: "success" };
    } else {
      return { label: "Very High", color: "error" };
    }
  };

  const dGH = calculateDGH(waterData.ca, waterData.mg);
  const dKH = calculateDKH(waterData.hco3, waterData.co3);
  const hardnessCategory = getWaterHardnessCategory(dGH);
  const alkalinityCategory = getAlkalinityCategory(dKH);

  return (
    <Card>
      <CardHeader
        title={waterData.name || "Water Quality Profile"}
        subheader="Water source parameters and composition"
        action={
          <Box sx={{ display: 'flex', gap: 1 }}>
            {onEdit && (
              <Button
                onClick={onEdit}
                color="primary"
                size="small"
                variant="outlined"
              >
                Edit Profile
              </Button>
            )}
            <Button
              aria-controls="export-menu"
              aria-haspopup="true"
              onClick={handleMenuClick}
              color="primary"
              size="small"
              variant="contained"
              startIcon={<FileDownloadIcon />}
              disabled={isLoading}
            >
              {isLoading ? <CircularProgress size={20} color="inherit" /> : 'Export'}
            </Button>
            <Menu
              id="export-menu"
              anchorEl={anchorEl}
              keepMounted
              open={Boolean(anchorEl)}
              onClose={handleMenuClose}
            >
              <MenuItem onClick={handleExportCsv}>
                <ListItemIcon>
                  <TableRowsIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText primary="Export as CSV" />
              </MenuItem>
              <MenuItem onClick={handleExportJson}>
                <ListItemIcon>
                  <CodeIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText primary="Export as JSON" />
              </MenuItem>
              <MenuItem onClick={handlePrint}>
                <ListItemIcon>
                  <PrintIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText primary="Print Profile" />
              </MenuItem>
              <MenuItem onClick={handleShare}>
                <ListItemIcon>
                  <ShareIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText primary="Share Profile" />
              </MenuItem>
            </Menu>
          </Box>
        }
      />
      <CardContent>
        <Grid container spacing={3}>
          {/* Basic Parameters */}
          <Grid size={{ xs: 12 }}>
            <Paper elevation={1} sx={{ p: 2 }}>
              <Typography variant="h6" gutterBottom>Basic Parameters</Typography>
              <Grid container spacing={2}>
                <Grid size={{ xs: 6, md: 3 }}>
                  <Typography variant="body2" color="text.secondary">pH</Typography>
                  <Typography variant="h6">{waterData.ph}</Typography>
                </Grid>
                <Grid size={{ xs: 6, md: 3 }}>
                  <Typography variant="body2" color="text.secondary">EC</Typography>
                  <Typography variant="h6">{waterData.ec} mS/cm</Typography>
                </Grid>
                <Grid size={{ xs: 6, md: 3 }}>
                  <Typography variant="body2" color="text.secondary">TDS</Typography>
                  <Typography variant="h6">{waterData.tds} ppm</Typography>
                </Grid>
                <Grid size={{ xs: 6, md: 3 }}>
                  <Typography variant="body2" color="text.secondary">Temperature</Typography>
                  <Typography variant="h6">{waterData.temperature} °C</Typography>
                </Grid>
              </Grid>
            </Paper>
          </Grid>

          {/* Water Hardness Section */}
          <Grid size={{ xs: 12, md: 6 }}>
            <Paper elevation={1} sx={{ p: 2 }}>
              <Grid container justifyContent="space-between" alignItems="center">
                <Typography variant="h6">Water Hardness</Typography>
                <Chip
                  label={hardnessCategory.label}
                  color={hardnessCategory.color}
                  size="small"
                />
              </Grid>
              <Divider sx={{ my: 1 }} />
              <Grid container spacing={2}>
                <Grid size={{ xs: 6 }}>
                  <Typography variant="body2" color="text.secondary">German Hardness (dGH)</Typography>
                  <Typography variant="h6">{dGH} °dH</Typography>
                </Grid>
                <Grid size={{ xs: 6 }}>
                  <Typography variant="body2" color="text.secondary">Calcium (Ca²⁺)</Typography>
                  <Typography variant="h6">
                    {waterData.ca} {ionUnit}
                  </Typography>
                </Grid>
                <Grid size={{ xs: 6 }}>
                  <Typography variant="body2" color="text.secondary">Magnesium (Mg²⁺)</Typography>
                  <Typography variant="h6">
                    {waterData.mg} {ionUnit}
                  </Typography>
                </Grid>
              </Grid>
            </Paper>
          </Grid>

          {/* Alkalinity Section */}
          <Grid size={{ xs: 12, md: 6 }}>
            <Paper elevation={1} sx={{ p: 2 }}>
              <Grid container justifyContent="space-between" alignItems="center">
                <Typography variant="h6">Alkalinity</Typography>
                <Chip
                  label={alkalinityCategory.label}
                  color={alkalinityCategory.color}
                  size="small"
                />
              </Grid>
              <Divider sx={{ my: 1 }} />
              <Grid container spacing={2}>
                <Grid size={{ xs: 6 }}>
                  <Typography variant="body2" color="text.secondary">Carbonate Hardness (dKH)</Typography>
                  <Typography variant="h6">{dKH} °dH</Typography>
                </Grid>
                <Grid size={{ xs: 6 }}>
                  <Typography variant="body2" color="text.secondary">Bicarbonate (HCO₃⁻)</Typography>
                  <Typography variant="h6">
                    {waterData.hco3} {ionUnit}
                  </Typography>
                </Grid>
                <Grid size={{ xs: 6 }}>
                  <Typography variant="body2" color="text.secondary">Carbonate (CO₃²⁻)</Typography>
                  <Typography variant="h6">
                    {waterData.co3} {ionUnit}
                  </Typography>
                </Grid>
              </Grid>
            </Paper>
          </Grid>

          {/* Ion Balance Section */}
          <Grid size={{ xs: 12 }}>
            <Paper elevation={1} sx={{ p: 2 }}>
              <Typography variant="h6" gutterBottom>Ion Composition</Typography>
              <Grid container spacing={2}>
                {/* Cations */}
                <Grid size={{ xs: 12, md: 6 }}>
                  <Typography variant="subtitle2" gutterBottom>Cations</Typography>
                  <Grid container spacing={2}>
                    <Grid size={{ xs: 6 }}>
                      <Typography variant="body2" color="text.secondary">Potassium (K⁺)</Typography>
                      <Typography>
                        {waterData.k} {ionUnit}
                      </Typography>
                    </Grid>
                    <Grid size={{ xs: 6 }}>
                      <Typography variant="body2" color="text.secondary">Sodium (Na⁺)</Typography>
                      <Typography>
                        {waterData.na} {ionUnit}
                      </Typography>
                    </Grid>
                  </Grid>
                </Grid>

                {/* Anions */}
                <Grid size={{ xs: 12, md: 6 }}>
                  <Typography variant="subtitle2" gutterBottom>Anions</Typography>
                  <Grid container spacing={2}>
                    <Grid size={{ xs: 6 }}>
                      <Typography variant="body2" color="text.secondary">Chloride (Cl⁻)</Typography>
                      <Typography>
                        {waterData.cl} {ionUnit}
                      </Typography>
                    </Grid>
                    <Grid size={{ xs: 6 }}>
                      <Typography variant="body2" color="text.secondary">Sulfate (SO₄²⁻)</Typography>
                      <Typography>
                        {waterData.so4} {ionUnit}
                      </Typography>
                    </Grid>
                    <Grid size={{ xs: 6 }}>
                      <Typography variant="body2" color="text.secondary">Nitrate (NO₃⁻)</Typography>
                      <Typography>
                        {waterData.no3} {ionUnit}
                      </Typography>
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </Paper>
          </Grid>
        </Grid>
      </CardContent>

      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbarSeverity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Card>
  );
};

export default WaterQualityDisplayUpdated;
