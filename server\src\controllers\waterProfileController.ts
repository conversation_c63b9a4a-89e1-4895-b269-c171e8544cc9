import { Request, Response } from 'express';
import { prisma } from '../app';

export const WaterProfileController = {
  // Get all water profiles
  getAllWaterProfiles: async (req: Request, res: Response) => {
    try {
      const { includeElements } = req.query;
      let waterProfiles;
      
      if (includeElements === 'true') {
        waterProfiles = await prisma.waterProfile.findMany({
          include: {
            elements: true,
            formulations: true
          }
        });
      } else {
        waterProfiles = await prisma.waterProfile.findMany();
      }
      
      return res.status(200).json(waterProfiles);
    } catch (error) {
      console.error(`Error fetching water profiles: ${error}`);
      return res.status(500).json({ error: 'Failed to fetch water profiles' });
    }
  },

  // Get water profile by ID
  getWaterProfileById: async (req: Request, res: Response) => {
    const { id } = req.params;
    const { includeElements } = req.query;
    
    try {
      let waterProfile;
      
      if (includeElements === 'true') {
        waterProfile = await prisma.waterProfile.findUnique({
          where: { id },
          include: {
            elements: true,
            formulations: true
          }
        });
      } else {
        waterProfile = await prisma.waterProfile.findUnique({
          where: { id }
        });
      }
      
      if (!waterProfile) {
        return res.status(404).json({ error: 'Water profile not found' });
      }
      
      return res.status(200).json(waterProfile);
    } catch (error) {
      console.error(`Error fetching water profile: ${error}`);
      return res.status(500).json({ error: 'Failed to fetch water profile' });
    }
  },

  // Create new water profile
  createWaterProfile: async (req: Request, res: Response) => {
    const { name, description, ec, ph, elements } = req.body;
    
    if (!name) {
      return res.status(400).json({ error: 'Water profile name is required' });
    }
    
    if (ec === undefined || ph === undefined) {
      return res.status(400).json({ error: 'EC and pH values are required' });
    }
    
    try {
      // Check if a water profile with the same name already exists
      const existingProfile = await prisma.waterProfile.findFirst({
        where: { name }
      });
      
      if (existingProfile) {
        return res.status(409).json({ error: `Water profile with name '${name}' already exists` });
      }
      
      // Start a transaction to create the water profile and its elements
      const result = await prisma.$transaction(async (prisma) => {
        // Create the water profile
        const waterProfile = await prisma.waterProfile.create({
          data: {
            name,
            description,
            ec: parseFloat(ec),
            ph: parseFloat(ph)
          }
        });
        
        // Add elements if provided
        if (elements && Array.isArray(elements) && elements.length > 0) {
          for (const elem of elements) {
            const { elementSymbol, concentration } = elem;
            
            if (!elementSymbol) {
              throw new Error('Element symbol is required for each water element');
            }
            
            if (concentration === undefined || isNaN(parseFloat(concentration))) {
              throw new Error('Valid concentration is required for each element in water profile');
            }
            
            // Create the water-element relation
            await prisma.waterElement.create({
              data: {
                waterProfileId: waterProfile.id,
                elementSymbol,
                concentration: parseFloat(concentration)
              }
            });
          }
        }
        
        // Return the water profile with its elements
        return prisma.waterProfile.findUnique({
          where: { id: waterProfile.id },
          include: {
            elements: true
          }
        });
      });
      
      return res.status(201).json(result);    } catch (error: any) {
      console.error(`Error creating water profile: ${error}`);
      return res.status(500).json({ error: `Failed to create water profile: ${error.message}` });
    }
  },

  // Update water profile
  updateWaterProfile: async (req: Request, res: Response) => {
    const { id } = req.params;
    const { name, description, ec, ph, elements } = req.body;
    
    try {
      // Check if water profile exists
      const existingProfile = await prisma.waterProfile.findUnique({
        where: { id }
      });
      
      if (!existingProfile) {
        return res.status(404).json({ error: 'Water profile not found' });
      }
      
      // Check if new name conflicts with existing water profile
      if (name && name !== existingProfile.name) {
        const nameConflict = await prisma.waterProfile.findFirst({
          where: { name }
        });
        
        if (nameConflict) {
          return res.status(409).json({ error: `Water profile with name '${name}' already exists` });
        }
      }
      
      // Parse numeric values
      let parsedEc = undefined;
      let parsedPh = undefined;
      
      if (ec !== undefined) {
        parsedEc = parseFloat(ec);
        if (isNaN(parsedEc)) {
          return res.status(400).json({ error: 'EC must be a valid number' });
        }
      }
      
      if (ph !== undefined) {
        parsedPh = parseFloat(ph);
        if (isNaN(parsedPh)) {
          return res.status(400).json({ error: 'pH must be a valid number' });
        }
      }
      
      // Update the water profile in a transaction
      const result = await prisma.$transaction(async (prisma) => {
        // Update the water profile properties
        const waterProfile = await prisma.waterProfile.update({
          where: { id },
          data: {
            ...(name && { name }),
            ...(description !== undefined && { description }),
            ...(parsedEc !== undefined && { ec: parsedEc }),
            ...(parsedPh !== undefined && { ph: parsedPh })
          }
        });
        
        // Update elements if provided
        if (elements && Array.isArray(elements)) {
          // First, remove all existing water-element relations
          await prisma.waterElement.deleteMany({
            where: { waterProfileId: id }
          });
          
          // Then create new relations
          for (const elem of elements) {
            const { elementSymbol, concentration } = elem;
            
            if (!elementSymbol) {
              throw new Error('Element symbol is required for each water element');
            }
            
            if (concentration === undefined || isNaN(parseFloat(concentration))) {
              throw new Error('Valid concentration is required for each element in water profile');
            }
            
            // Create the water-element relation
            await prisma.waterElement.create({
              data: {
                waterProfileId: waterProfile.id,
                elementSymbol,
                concentration: parseFloat(concentration)
              }
            });
          }
        }
        
        // Return the updated water profile with its elements
        return prisma.waterProfile.findUnique({
          where: { id: waterProfile.id },
          include: {
            elements: true
          }
        });
      });
      
      return res.status(200).json(result);    } catch (error: any) {
      console.error(`Error updating water profile: ${error}`);
      return res.status(500).json({ error: `Failed to update water profile: ${error.message}` });
    }
  },

  // Delete water profile
  deleteWaterProfile: async (req: Request, res: Response) => {
    const { id } = req.params;
    
    try {
      // Check if the water profile is used in any formulations
      const formulationCount = await prisma.formulation.count({
        where: { waterProfileId: id }
      });
      
      if (formulationCount > 0) {
        return res.status(409).json({ 
          error: 'Cannot delete water profile as it is used in one or more formulations',
          count: formulationCount
        });
      }
      
      // Use a transaction to delete water elements and the water profile
      await prisma.$transaction(async (prisma) => {
        // Delete all water-element relations
        await prisma.waterElement.deleteMany({
          where: { waterProfileId: id }
        });
        
        // Delete the water profile
        await prisma.waterProfile.delete({
          where: { id }
        });
      });
      
      return res.status(204).send();
    } catch (error) {
      console.error(`Error deleting water profile: ${error}`);
      return res.status(500).json({ error: 'Failed to delete water profile' });
    }
  },
  
  // Search water profiles
  searchWaterProfiles: async (req: Request, res: Response) => {
    const { term } = req.query;
    
    if (!term || typeof term !== 'string') {
      return res.status(400).json({ error: 'Search term is required' });
    }
    
    try {
      const waterProfiles = await prisma.waterProfile.findMany({
        where: {
          OR: [
            { name: { contains: term } },
            { description: { contains: term } }
          ]
        },
        include: {
          elements: true
        }
      });
      
      return res.status(200).json(waterProfiles);
    } catch (error) {
      console.error(`Error searching water profiles: ${error}`);
      return res.status(500).json({ error: 'Failed to search water profiles' });
    }
  }
};
