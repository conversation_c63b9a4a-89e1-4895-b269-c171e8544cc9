import { PrismaClient } from '@prisma/client';

// Add Node.js process type
import * as process from 'process';

const prisma = new PrismaClient();

async function main() {
  console.log('Starting database seeding...');

  // Define common elements used in hydroponics
  const elements = [
    { symbol: 'N', name: 'Nitrogen', atomicWeight: 14.0067 },
    { symbol: 'P', name: 'Phosphorus', atomicWeight: 30.9738 },
    { symbol: 'K', name: 'Potassium', atomicWeight: 39.0983 },
    { symbol: 'Ca', name: '<PERSON>cium', atomicWeight: 40.078 },
    { symbol: 'Mg', name: 'Magnesium', atomicWeight: 24.305 },
    { symbol: 'S', name: '<PERSON><PERSON><PERSON>', atomicWeight: 32.065 },
    { symbol: 'Fe', name: 'Iron', atomicWeight: 55.845 },
    { symbol: 'Mn', name: 'Manganese', atomicWeight: 54.938 },
    { symbol: 'Zn', name: '<PERSON>in<PERSON>', atomicWeight: 65.38 },
    { symbol: 'Cu', name: '<PERSON>', atomicWeight: 63.546 },
    { symbol: '<PERSON>', name: '<PERSON><PERSON>', atomicWeight: 10.811 },
    { symbol: '<PERSON>', name: 'Molybdenum', atomicWeight: 95.96 },
    { symbol: 'Cl', name: 'Chlorine', atomicWeight: 35.453 },
    { symbol: 'Si', name: 'Silicon', atomicWeight: 28.0855 },
    { symbol: 'Na', name: 'Sodium', atomicWeight: 22.9897 },
    { symbol: 'O', name: 'Oxygen', atomicWeight: 15.9994 },
    { symbol: 'H', name: 'Hydrogen', atomicWeight: 1.00794 },
    { symbol: 'C', name: 'Carbon', atomicWeight: 12.0107 },
  ];

  console.log('Seeding elements...');
  for (const element of elements) {
    await prisma.element.upsert({
      where: { symbol: element.symbol },
      update: element,
      create: element
    });
  }

  // Define common fertilizer salts used in hydroponics
  const salts = [
    { name: 'Calcium Nitrate', formula: 'Ca(NO3)2', density: 1.1, solubility: 1290 },
    { name: 'Potassium Nitrate', formula: 'KNO3', density: 1.05, solubility: 360 },
    { name: 'Magnesium Nitrate', formula: 'Mg(NO3)2', density: 1.1, solubility: 710 },
    { name: 'Potassium Sulfate', formula: 'K2SO4', density: 1.0, solubility: 120 },
    { name: 'Potassium Phosphate Monobasic', formula: 'KH2PO4', density: 1.0, solubility: 226 },
    { name: 'Magnesium Sulfate', formula: 'MgSO4', density: 1.0, solubility: 710 },
    { name: 'Ammonium Nitrate', formula: 'NH4NO3', density: 1.0, solubility: 1900 },
    { name: 'Ferrous Sulfate', formula: 'FeSO4', density: 1.0, solubility: 156 },
    { name: 'Manganese Sulfate', formula: 'MnSO4', density: 1.0, solubility: 980 },
    { name: 'Boric Acid', formula: 'H3BO3', density: 1.0, solubility: 57 },
    { name: 'Copper Sulfate', formula: 'CuSO4', density: 1.0, solubility: 220 },
    { name: 'Zinc Sulfate', formula: 'ZnSO4', density: 1.0, solubility: 965 },
    { name: 'Sodium Molybdate', formula: 'Na2MoO4', density: 1.0, solubility: 840 },
  ];

  console.log('Seeding salts...');
  const saltEntities: any[] = [];
  for (const salt of salts) {
    const createdSalt = await prisma.salt.create({
      data: salt
    });
    saltEntities.push(createdSalt as any);
  }

  // Define the salt compositions (element percentages)
  const saltCompositions = [
    // Calcium Nitrate: 19% Ca, 15.5% N
    { saltName: 'Calcium Nitrate', compositions: [
        { symbol: 'Ca', percentage: 19.0 },
        { symbol: 'N', percentage: 15.5 }
      ]
    },
    // Potassium Nitrate: 38% K, 13% N
    { saltName: 'Potassium Nitrate', compositions: [
        { symbol: 'K', percentage: 38.0 },
        { symbol: 'N', percentage: 13.0 }
      ]
    },
    // Magnesium Nitrate: 9.5% Mg, 10.5% N
    { saltName: 'Magnesium Nitrate', compositions: [
        { symbol: 'Mg', percentage: 9.5 },
        { symbol: 'N', percentage: 10.5 }
      ]
    },
    // Potassium Sulfate: 45% K, 18% S
    { saltName: 'Potassium Sulfate', compositions: [
        { symbol: 'K', percentage: 45.0 },
        { symbol: 'S', percentage: 18.0 }
      ]
    },
    // Potassium Phosphate Monobasic: 28% K, 23% P
    { saltName: 'Potassium Phosphate Monobasic', compositions: [
        { symbol: 'K', percentage: 28.0 },
        { symbol: 'P', percentage: 23.0 }
      ]
    },
    // Magnesium Sulfate: 9.8% Mg, 13% S
    { saltName: 'Magnesium Sulfate', compositions: [
        { symbol: 'Mg', percentage: 9.8 },
        { symbol: 'S', percentage: 13.0 }
      ]
    },
    // Ammonium Nitrate: 33% N
    { saltName: 'Ammonium Nitrate', compositions: [
        { symbol: 'N', percentage: 33.0 }
      ]
    },
    // Ferrous Sulfate: 20% Fe, 11% S
    { saltName: 'Ferrous Sulfate', compositions: [
        { symbol: 'Fe', percentage: 20.0 },
        { symbol: 'S', percentage: 11.0 }
      ]
    },
    // Manganese Sulfate: 32% Mn, 19% S
    { saltName: 'Manganese Sulfate', compositions: [
        { symbol: 'Mn', percentage: 32.0 },
        { symbol: 'S', percentage: 19.0 }
      ]
    },
    // Boric Acid: 17.5% B
    { saltName: 'Boric Acid', compositions: [
        { symbol: 'B', percentage: 17.5 }
      ]
    },
    // Copper Sulfate: 25% Cu, 12% S
    { saltName: 'Copper Sulfate', compositions: [
        { symbol: 'Cu', percentage: 25.0 },
        { symbol: 'S', percentage: 12.0 }
      ]
    },
    // Zinc Sulfate: 36% Zn, 18% S
    { saltName: 'Zinc Sulfate', compositions: [
        { symbol: 'Zn', percentage: 36.0 },
        { symbol: 'S', percentage: 18.0 }
      ]
    },
    // Sodium Molybdate: 39% Mo
    { saltName: 'Sodium Molybdate', compositions: [
        { symbol: 'Mo', percentage: 39.0 },
        { symbol: 'Na', percentage: 19.0 }
      ]
    },
  ];

  console.log('Seeding salt compositions...');
  for (const composition of saltCompositions) {
    const salt = await prisma.salt.findFirst({
      where: { name: composition.saltName }
    });
    
    if (salt) {
      for (const elementComp of composition.compositions) {
        const element = await prisma.element.findUnique({
          where: { symbol: elementComp.symbol }
        });
        
        if (element) {
          await prisma.saltElement.create({
            data: {
              saltId: salt.id,
              elementId: element.id,
              percentage: elementComp.percentage
            }
          });
        }
      }
    }
  }

  // Define common water profiles
  const waterProfiles = [
    {
      name: 'Pure RO/Distilled Water',
      description: 'Water with virtually no minerals',
      ec: 0.0,
      ph: 7.0,
      elements: []
    },
    {
      name: 'Tap Water (Average)',
      description: 'Average municipal tap water',
      ec: 0.4,
      ph: 7.2,
      elements: [
        { symbol: 'Ca', concentration: 40.0 },
        { symbol: 'Mg', concentration: 10.0 },
        { symbol: 'Na', concentration: 15.0 },
        { symbol: 'Cl', concentration: 20.0 },
        { symbol: 'S', concentration: 5.0 }
      ]
    },
    {
      name: 'Hard Well Water',
      description: 'Water with high mineral content',
      ec: 0.8,
      ph: 7.5,
      elements: [
        { symbol: 'Ca', concentration: 80.0 },
        { symbol: 'Mg', concentration: 25.0 },
        { symbol: 'Na', concentration: 20.0 },
        { symbol: 'Cl', concentration: 30.0 },
        { symbol: 'S', concentration: 15.0 },
        { symbol: 'Fe', concentration: 0.3 }
      ]
    }
  ];

  console.log('Seeding water profiles...');
  for (const profile of waterProfiles) {
    const { elements: waterElements, ...profileData } = profile;
    
    const createdProfile = await prisma.waterProfile.create({
      data: profileData
    });
    
    for (const elem of waterElements) {
      await prisma.waterElement.create({
        data: {
          waterProfileId: createdProfile.id,
          elementSymbol: elem.symbol,
          concentration: elem.concentration
        }
      });
    }
  }

  // Create a sample formulation
  console.log('Creating sample formulation...');
  const waterProfile = await prisma.waterProfile.findFirst({
    where: { name: 'Pure RO/Distilled Water' }
  });
  
  if (waterProfile) {
    const formulation = await prisma.formulation.create({
      data: {
        name: 'General Purpose Hydroponic Solution',
        description: 'A balanced nutrient solution for leafy greens',
        waterProfileId: waterProfile.id,
        targetEc: 1.8,
        targetPh: 5.8,
        volume: 20,
        volumeUnit: 'L'
      }
    });

    // Add target elements to the formulation
    const targetElements = [
      { symbol: 'N', concentration: 150.0 },
      { symbol: 'P', concentration: 50.0 },
      { symbol: 'K', concentration: 200.0 },
      { symbol: 'Ca', concentration: 120.0 },
      { symbol: 'Mg', concentration: 40.0 },
      { symbol: 'S', concentration: 50.0 },
      { symbol: 'Fe', concentration: 3.0 },
      { symbol: 'Mn', concentration: 0.5 },
      { symbol: 'Zn', concentration: 0.15 },
      { symbol: 'Cu', concentration: 0.15 },
      { symbol: 'B', concentration: 0.5 },
      { symbol: 'Mo', concentration: 0.05 }
    ];
    
    for (const target of targetElements) {
      const element = await prisma.element.findUnique({
        where: { symbol: target.symbol }
      });
      
      if (element) {
        await prisma.targetElement.create({
          data: {
            formulationId: formulation.id,
            elementId: element.id,
            concentration: target.concentration,
            unit: 'ppm'
          }
        });
      }
    }
    
    // Add sample salt quantities to the formulation
    const saltQuantities = [
      { name: 'Calcium Nitrate', amount: 77.0 },
      { name: 'Potassium Nitrate', amount: 50.6 },
      { name: 'Magnesium Sulfate', amount: 49.2 },
      { name: 'Potassium Phosphate Monobasic', amount: 27.2 },
      { name: 'Ferrous Sulfate', amount: 1.5 }
    ];
    
    for (const saltQ of saltQuantities) {
      const salt = await prisma.salt.findFirst({
        where: { name: saltQ.name }
      });
      
      if (salt) {
        await prisma.formulationSalt.create({
          data: {
            formulationId: formulation.id,
            saltId: salt.id,
            amount: saltQ.amount,
            unit: 'g'
          }
        });
      }
    }
  }

  console.log('Database seeding completed successfully!');
}

main()
  .catch((e) => {
    console.error('Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
