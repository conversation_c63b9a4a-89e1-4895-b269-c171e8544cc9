// filepath: /hydrobuddy-pwa/hydrobuddy-pwa/server/src/services/scaling.ts

import { ScalingParameters, ScalingResult } from '../models/scalingModel';

/**
 * Scales the input values based on the provided scaling parameters.
 * @param {ScalingParameters} params - The parameters for scaling.
 * @returns {ScalingResult} - The result of the scaling operation.
 */
export function scaleValues(params: ScalingParameters): ScalingResult {
    const { inputValues, scaleFactor } = params;
    const scaledValues = inputValues.map(value => value * scaleFactor);
    
    return {
        originalValues: inputValues,
        scaledValues: scaledValues,
        scaleFactor: scaleFactor
    };
}

/**
 * Validates the scaling parameters.
 * @param {ScalingParameters} params - The parameters to validate.
 * @returns {boolean} - True if valid, false otherwise.
 */
export function validateScalingParameters(params: ScalingParameters): boolean {
    const { inputValues, scaleFactor } = params;
    if (!Array.isArray(inputValues) || inputValues.length === 0) {
        return false;
    }
    if (typeof scaleFactor !== 'number' || scaleFactor <= 0) {
        return false;
    }
    return true;
}